<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HeroSlider extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'subtitle',
        'description',
        'image',
        'button_text',
        'button_link',
        'button_color',
        'sort_order',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Scope for active sliders
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope for ordered sliders
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    // Get button color class
    public function getButtonColorClassAttribute()
    {
        $colors = [
            'primary' => 'btn-primary',
            'secondary' => 'btn-secondary',
            'success' => 'btn-success',
            'danger' => 'btn-danger',
            'warning' => 'btn-warning',
            'info' => 'btn-info',
            'light' => 'btn-light',
            'dark' => 'btn-dark',
        ];

        return $colors[$this->button_color] ?? 'btn-primary';
    }
}
