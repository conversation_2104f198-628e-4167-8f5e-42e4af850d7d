@extends('layouts.app')

@section('title', 'เกี่ยวกับเรา - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<!-- Hero Section with Background Image -->
@if($aboutPage->hero_image)
<section class="hero-section position-relative" style="background: linear-gradient(rgba(139, 69, 19, 0.8), rgba(139, 69, 19, 0.8)), url('{{ asset('storage/' . $aboutPage->hero_image) }}'); background-size: cover; background-position: center; min-height: 30vh;">
@else
<section class="py-5 bg-primary text-white">
@endif
    <div class="container">
        <div class="row align-items-center" style="min-height: 25vh;">
            <div class="col-lg-8 mx-auto text-center text-white">
                <h1 class="h2 fw-bold mb-2">
                    <i class="fas fa-info-circle me-3"></i>{{ $aboutPage->title ?? 'เกี่ยวกับเรา' }}
                </h1>
                <p class="fs-6 mb-2">
                    {{ $aboutPage->content ?? 'ความเป็นมาและเรื่องราวของร้านก๋วยเตี๋ยวเรือเข้าท่า' }}
                </p>
                @if($aboutPage->hero_image)
                <div class="mt-2">
                    <div class="d-inline-block bg-white bg-opacity-25 rounded-pill px-3 py-1">
                        <i class="fas fa-ship me-1"></i>
                        <span style="font-size: 0.9rem;">ร้านก๋วยเตี๋ยวเรือแท้</span>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Story Section -->
<section class="py-5">
    <div class="container">
        <div class="row align-items-center mb-5">
            <div class="col-lg-6">
                <h2 class="text-primary mb-4">เรื่องราวของเรา</h2>
                <div class="lead">
                    {!! nl2br(e($aboutPage->our_story ?? 'เริ่มต้นจากความฝันของคุณยายที่ต้องการนำเสนอรสชาติก๋วยเตี๋ยวเรือแท้ๆ ให้คนรุ่นใหม่ได้สัมผัส ด้วยสูตรลับที่ถ่ายทอดมาจากรุ่นสู่รุ่น เราจึงเปิดร้านเล็กๆ จนมาและค่อยๆ เติบโตมาจนถึงทุกวันนี้

by toas')) !!}
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    @if($aboutPage->story_image)
                        <img src="{{ asset('storage/' . $aboutPage->story_image) }}"
                             alt="เรื่องราวของเรา" class="img-fluid rounded shadow">
                    @else
                        <img src="{{ asset('images/restaurant/boat-noodle-shop.jpg') }}"
                             alt="ร้านก๋วยเตี๋ยวเรือเข้าท่า" class="img-fluid rounded shadow">
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Main Content Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-5">
                        <h2 class="text-primary mb-4 text-center">
                            <i class="fas fa-info-circle me-2"></i>เนื้อหาหลัก
                        </h2>
                        <div class="lead text-muted">
                            {!! nl2br(e($aboutPage->main_content ?? 'ร้านก๋วยเตี๋ยวเรือเข้าท่า เปิดบริการก๋วยเตี๋ยวเรือรสชาติดั้งเดิม สืบทอดสูตรลับจากอดีตสู่ปัจจุบัน สินค้าดำรงรักษาคุณภาพก๋วยเตี๋ยวเรือแท้

เรือแก้ว มาอย่างต่อเนื่อง ด้วยความใส่ใจในคุณภาพของอาหารและระบบการให้บริการที่ก่อให้เกิด
เรือแก้ว มาอย่างต่อเนื่อง ด้วยความใส่ใจในคุณภาพของอาหารและระบบการให้บริการที่ก่อให้เกิด

by toas')) !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Logo & Brand Section -->
<section class="py-5 bg-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 text-center mb-4 mb-lg-0">
                @if($restaurantInfo->logo)
                    <div class="mb-4">
                        <img src="{{ asset('storage/' . $restaurantInfo->logo) }}"
                             alt="{{ $restaurantInfo->name ?? 'ร้านก๋วยเตี๋ยวเรือเข้าท่า' }}"
                             class="img-fluid rounded-circle shadow-lg"
                             style="max-width: 250px; max-height: 250px; object-fit: cover;">
                    </div>
                @else
                    <div class="mb-4">
                        <div class="bg-primary rounded-circle mx-auto d-flex align-items-center justify-content-center shadow-lg"
                             style="width: 250px; height: 250px;">
                            <i class="fas fa-ship fa-5x text-white"></i>
                        </div>
                    </div>
                @endif
                <h3 class="text-primary mb-2">{{ $restaurantInfo->name ?? 'ร้านก๋วยเตี๋ยวเรือเข้าท่า' }}</h3>
                <p class="text-muted">{{ $restaurantInfo->description ?? 'ร้านก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม' }}</p>
            </div>
            <div class="col-lg-6">
                <div class="ps-lg-4">
                    <h4 class="text-primary mb-4">เอกลักษณ์ของเรา</h4>
                    <div class="row">
                        <div class="col-sm-6 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle p-2 me-3">
                                    <i class="fas fa-award text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">รสชาติแท้</h6>
                                    <small class="text-muted">สูตรดั้งเดิม</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-success rounded-circle p-2 me-3">
                                    <i class="fas fa-leaf text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">วัตถุดิบสด</h6>
                                    <small class="text-muted">คุณภาพดี</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-info rounded-circle p-2 me-3">
                                    <i class="fas fa-clock text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">เสิร์ฟเร็ว</h6>
                                    <small class="text-muted">บริการรวดเร็ว</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-warning rounded-circle p-2 me-3">
                                    <i class="fas fa-heart text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">ใส่ใจ</h6>
                                    <small class="text-muted">ทุกรายละเอียด</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Mission & Vision Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-bullseye fa-3x text-primary mb-3"></i>
                        <h5 class="card-title text-primary">พันธกิจ</h5>
                        <p class="card-text">
                            {{ $aboutPage->our_mission ?? 'มุ่งมั่นที่จะนำเสนออาหารไทยต้นตำรับที่มีคุณภาพ ด้วยวัตถุดิบสดใหม่และการบริการที่เป็นมิตร เพื่อสร้างความประทับใจให้กับลูกค้าทุกท่าน' }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-eye fa-3x text-success mb-3"></i>
                        <h5 class="card-title text-success">วิสัยทัศน์</h5>
                        <p class="card-text">
                            {{ $aboutPage->our_vision ?? 'เป็นร้านก๋วยเตี๋ยวเรือที่เป็นที่รู้จักและได้รับการยอมรับในด้านรสชาติและคุณภาพ พร้อมขยายสาขาเพื่อให้คนไทยทุกภูมิภาคได้ลิ้มรสก๋วยเตี๋ยวเรือแท้ๆ' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Values Section -->
<section class="py-5 bg-white">
    <div class="container">
        <h2 class="text-center text-primary mb-5">ค่านิยมของเรา</h2>
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="card h-100 border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="fas fa-heart fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">ความรักในการทำอาหาร</h5>
                        <p class="card-text">
                            เราใส่ใจในทุกขั้นตอนการทำอาหาร ด้วยความรักและความตั้งใจ 
                            เพื่อให้ลูกค้าได้รับประทานอาหารที่อร่อยที่สุด
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card h-100 border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="fas fa-leaf fa-3x text-success mb-3"></i>
                        <h5 class="card-title">วัตถุดิบคุณภาพ</h5>
                        <p class="card-text">
                            เราคัดสรรวัตถุดิบสดใหม่และมีคุณภาพดีที่สุด 
                            เพื่อให้ได้รสชาติที่เข้มข้นและหอมหวน
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card h-100 border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="fas fa-users fa-3x text-info mb-3"></i>
                        <h5 class="card-title">บริการที่เป็นมิตร</h5>
                        <p class="card-text">
                            เราให้ความสำคัญกับการบริการที่เป็นมิตร อบอุ่น 
                            และใส่ใจในความต้องการของลูกค้าทุกท่าน
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Specialty Section -->
<section class="py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 order-lg-2">
                <h2 class="text-primary mb-4">ความพิเศษของเรา</h2>
                <div class="mb-4">
                    <h5><i class="fas fa-star text-warning me-2"></i>สูตรลับดั้งเดิม</h5>
                    <p>น้ำซุปก๋วยเตี๋ยวเรือที่ต้มด้วยเครื่องเทศมากกว่า 10 ชนิด ให้รสชาติเข้มข้นและหอมหวน</p>
                </div>
                
                <div class="mb-4">
                    <h5><i class="fas fa-fire text-danger me-2"></i>ความร้อนแรงที่ลงตัว</h5>
                    <p>เครื่องเทศและพริกที่ปรุงรสอย่างลงตัว ให้ความร้อนแรงที่กลมกล่อม ไม่แสบจนเกินไป</p>
                </div>
                
                <div class="mb-4">
                    <h5><i class="fas fa-clock text-primary me-2"></i>การเตรียมที่ใช้เวลา</h5>
                    <p>น้ำซุปที่ต้มมาอย่างยาวนาน เพื่อให้ได้รสชาติที่เข้มข้นและครบรส</p>
                </div>
            </div>
            <div class="col-lg-6 order-lg-1">
                <div class="text-center">
                    <img src="{{ asset('images/menu/noodle-beef-special.svg') }}"
                         alt="ก๋วยเตี๋ยวเรือเนื้อพิเศษ" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Section -->
@if($aboutPage->gallery_image_1 || $aboutPage->gallery_image_2 || $aboutPage->gallery_image_3)
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center text-primary mb-5">
            <i class="fas fa-images me-2"></i>แกลเลอรี่
        </h2>
        <div class="row g-4">
            @if($aboutPage->gallery_image_1)
            <div class="col-lg-4 col-md-6">
                <div class="gallery-item">
                    <img src="{{ asset('storage/' . $aboutPage->gallery_image_1) }}"
                         alt="แกลเลอรี่ 1"
                         class="img-fluid rounded shadow-sm gallery-img"
                         data-bs-toggle="modal"
                         data-bs-target="#galleryModal"
                         data-bs-image="{{ asset('storage/' . $aboutPage->gallery_image_1) }}">
                    <div class="gallery-overlay">
                        <i class="fas fa-search-plus fa-2x text-white"></i>
                    </div>
                </div>
            </div>
            @endif

            @if($aboutPage->gallery_image_2)
            <div class="col-lg-4 col-md-6">
                <div class="gallery-item">
                    <img src="{{ asset('storage/' . $aboutPage->gallery_image_2) }}"
                         alt="แกลเลอรี่ 2"
                         class="img-fluid rounded shadow-sm gallery-img"
                         data-bs-toggle="modal"
                         data-bs-target="#galleryModal"
                         data-bs-image="{{ asset('storage/' . $aboutPage->gallery_image_2) }}">
                    <div class="gallery-overlay">
                        <i class="fas fa-search-plus fa-2x text-white"></i>
                    </div>
                </div>
            </div>
            @endif

            @if($aboutPage->gallery_image_3)
            <div class="col-lg-4 col-md-6">
                <div class="gallery-item">
                    <img src="{{ asset('storage/' . $aboutPage->gallery_image_3) }}"
                         alt="แกลเลอรี่ 3"
                         class="img-fluid rounded shadow-sm gallery-img"
                         data-bs-toggle="modal"
                         data-bs-target="#galleryModal"
                         data-bs-image="{{ asset('storage/' . $aboutPage->gallery_image_3) }}">
                    <div class="gallery-overlay">
                        <i class="fas fa-search-plus fa-2x text-white"></i>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</section>
@endif

<!-- Team Section -->
@if($aboutPage->team_image)
<section class="py-5 bg-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <h2 class="text-primary mb-4">
                    <i class="fas fa-users me-2"></i>ทีมงานของเรา
                </h2>
                <p class="lead">
                    ทีมงานที่มีประสบการณ์และความเชี่ยวชาญในการทำก๋วยเตี๋ยวเรือ
                    พร้อมให้บริการด้วยใจและรอยยิ้ม
                </p>
                <p>
                    เราเป็นครอบครัวเดียวกันที่มีความมุ่งมั่นในการนำเสนออาหารที่มีคุณภาพ
                    และสร้างความประทับใจให้กับลูกค้าทุกท่าน
                </p>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <img src="{{ asset('storage/' . $aboutPage->team_image) }}"
                         alt="ทีมงานของเรา"
                         class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </div>
</section>
@endif

<!-- Contact Information -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <h2 class="text-center mb-5">ข้อมูลติดต่อ</h2>
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="text-center">
                    <i class="fas fa-map-marker-alt fa-3x mb-3"></i>
                    <h5>ที่อยู่ร้าน</h5>
                    <p>
                        {{ $restaurantInfo->address ?? '123 ถนนริมน้า แขวงพระบรมมหาราชวัง เขตพระนคร กรุงเทพมหานคร 10200' }}
                    </p>
                </div>
            </div>

            <div class="col-lg-4 mb-4">
                <div class="text-center">
                    <i class="fas fa-phone fa-3x mb-3"></i>
                    <h5>โทรศัพท์</h5>
                    <p>
                        {{ $restaurantInfo->phone ?? '02-123-4567' }}
                        @if($restaurantInfo->mobile)
                            <br>{{ $restaurantInfo->mobile }}
                        @endif
                    </p>
                </div>
            </div>

            <div class="col-lg-4 mb-4">
                <div class="text-center">
                    <i class="fas fa-clock fa-3x mb-3"></i>
                    <h5>เวลาเปิด-ปิด</h5>
                    <p>
                        {{ $restaurantInfo->opening_hours ?? 'เปิดทุกวัน 08:00 - 20:00 น.' }}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="{{ route('menu.index') }}" class="btn btn-light btn-lg">
                <i class="fas fa-utensils me-2"></i>ดูเมนูอาหาร
            </a>
        </div>
    </div>
</section>

<!-- Map Section (Placeholder) -->
<section class="py-5">
    <div class="container">
        <h2 class="text-center text-primary mb-4">แผนที่ร้าน</h2>
        <div class="row">
            <div class="col-12">
                <div class="bg-light rounded p-5 text-center">
                    <i class="fas fa-map fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">แผนที่ Google Maps</h5>
                    <p class="text-muted">สามารถเพิ่ม Google Maps embed code ที่นี่</p>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Gallery Modal -->
<div class="modal fade" id="galleryModal" tabindex="-1" aria-labelledby="galleryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="galleryModalLabel">แกลเลอรี่</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Gallery Image" class="img-fluid rounded">
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.hero-section {
    position: relative;
    overflow: hidden;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
}

.gallery-img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(139, 69, 19, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-item:hover .gallery-img {
    transform: scale(1.1);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gallery modal functionality
    const galleryModal = document.getElementById('galleryModal');
    const modalImage = document.getElementById('modalImage');

    if (galleryModal) {
        galleryModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const imageSrc = button.getAttribute('data-bs-image');
            modalImage.src = imageSrc;
        });
    }
});
</script>
@endpush

@endsection
