<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class AuthController extends Controller
{
    public function showLoginForm()
    {
        if (Auth::check()) {
            return redirect()->intended(route('home'));
        }
        return view('auth.login');
    }

    public function login(Request $request)
    {
        \Log::info('Login attempt started', ['email' => $request->email]);

        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            \Log::warning('Login validation failed', ['errors' => $validator->errors()]);
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $credentials = $request->only('email', 'password');
        $remember = $request->has('remember');

        \Log::info('Attempting authentication', ['email' => $credentials['email']]);

        if (Auth::attempt($credentials, $remember)) {
            \Log::info('Authentication successful', ['user_id' => Auth::id(), 'user_role' => Auth::user()->role]);

            $request->session()->regenerate();

            if (Auth::user()->isAdmin()) {
                \Log::info('Redirecting admin to dashboard');
                $response = redirect('/admin')->with('success', 'เข้าสู่ระบบสำเร็จ');
                \Log::info('Redirect response created', ['status' => $response->getStatusCode()]);
                return $response;
            }

            \Log::info('Redirecting user to home');
            return redirect('/')->with('success', 'เข้าสู่ระบบสำเร็จ');
        }

        \Log::warning('Authentication failed', ['email' => $credentials['email']]);
        return redirect()->back()
            ->withErrors(['email' => 'อีเมลหรือรหัสผ่านไม่ถูกต้อง'])
            ->withInput();
    }

    public function showRegisterForm()
    {
        if (Auth::check()) {
            return redirect()->intended(route('home'));
        }
        return view('auth.register');
    }

    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ], [
            'name.required' => 'กรุณากรอกชื่อ-นามสกุล',
            'email.required' => 'กรุณากรอกอีเมล',
            'email.email' => 'รูปแบบอีเมลไม่ถูกต้อง',
            'email.unique' => 'อีเมลนี้ถูกใช้งานแล้ว',
            'password.required' => 'กรุณากรอกรหัสผ่าน',
            'password.min' => 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร',
            'password.confirmed' => 'รหัสผ่านไม่ตรงกัน',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Log for debugging
            \Log::info('Creating user with data:', $request->only(['name', 'email']));

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => 'user', // Default role
            ]);

            \Log::info('User created successfully:', ['user_id' => $user->id]);

            // Login the user immediately after registration
            Auth::login($user, true); // Remember the user

            \Log::info('User logged in successfully');

            // Regenerate session for security
            $request->session()->regenerate();

            \Log::info('Session regenerated, redirecting to home');

            return redirect()->route('home')
                ->with('success', 'สมัครสมาชิกเรียบร้อยแล้ว ยินดีต้อนรับ ' . $user->name . '!');

        } catch (\Exception $e) {
            \Log::error('Registration failed:', ['error' => $e->getMessage()]);
            return redirect()->back()
                ->withErrors(['email' => 'เกิดข้อผิดพลาดในการสมัครสมาชิก: ' . $e->getMessage()])
                ->withInput();
        }
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }
}
