@extends('layouts.admin')

@section('title', 'แก้ไขสไลด์')

@section('content')
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>แก้ไขสไลด์
                    </h4>
                </div>

                <div class="card-body">
                    <form action="{{ route('admin.hero-sliders.update', $heroSlider) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="title" class="form-label">หัวข้อ <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('title') is-invalid @enderror" 
                                       id="title" 
                                       name="title" 
                                       value="{{ old('title', $heroSlider->title) }}" 
                                       required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="subtitle" class="form-label">หัวข้อรอง</label>
                                <input type="text" 
                                       class="form-control @error('subtitle') is-invalid @enderror" 
                                       id="subtitle" 
                                       name="subtitle" 
                                       value="{{ old('subtitle', $heroSlider->subtitle) }}">
                                @error('subtitle')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">คำอธิบาย</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" 
                                      name="description" 
                                      rows="3">{{ old('description', $heroSlider->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">รูปภาพ</label>
                            @if($heroSlider->image)
                                <div class="mb-2">
                                    <img src="{{ asset('storage/' . $heroSlider->image) }}" 
                                         alt="{{ $heroSlider->title }}" 
                                         class="img-thumbnail" 
                                         style="max-width: 200px;">
                                    <p class="text-muted small mt-1">รูปภาพปัจจุบัน</p>
                                </div>
                            @endif
                            <input type="file" 
                                   class="form-control @error('image') is-invalid @enderror" 
                                   id="image" 
                                   name="image" 
                                   accept="image/*">
                            <div class="form-text">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB) - เว้นว่างหากไม่ต้องการเปลี่ยน</div>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="button_text" class="form-label">ข้อความปุ่ม</label>
                                <input type="text" 
                                       class="form-control @error('button_text') is-invalid @enderror" 
                                       id="button_text" 
                                       name="button_text" 
                                       value="{{ old('button_text', $heroSlider->button_text) }}">
                                @error('button_text')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="button_link" class="form-label">ลิงก์ปุ่ม</label>
                                <input type="url" 
                                       class="form-control @error('button_link') is-invalid @enderror" 
                                       id="button_link" 
                                       name="button_link" 
                                       value="{{ old('button_link', $heroSlider->button_link) }}">
                                @error('button_link')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="button_color" class="form-label">สีปุ่ม <span class="text-danger">*</span></label>
                                <select class="form-select @error('button_color') is-invalid @enderror" 
                                        id="button_color" 
                                        name="button_color" 
                                        required>
                                    <option value="">เลือกสีปุ่ม</option>
                                    <option value="primary" {{ old('button_color', $heroSlider->button_color) == 'primary' ? 'selected' : '' }}>น้ำเงิน</option>
                                    <option value="success" {{ old('button_color', $heroSlider->button_color) == 'success' ? 'selected' : '' }}>เขียว</option>
                                    <option value="warning" {{ old('button_color', $heroSlider->button_color) == 'warning' ? 'selected' : '' }}>เหลือง</option>
                                    <option value="danger" {{ old('button_color', $heroSlider->button_color) == 'danger' ? 'selected' : '' }}>แดง</option>
                                    <option value="info" {{ old('button_color', $heroSlider->button_color) == 'info' ? 'selected' : '' }}>ฟ้า</option>
                                    <option value="dark" {{ old('button_color', $heroSlider->button_color) == 'dark' ? 'selected' : '' }}>ดำ</option>
                                </select>
                                @error('button_color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                                <input type="number" 
                                       class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       value="{{ old('sort_order', $heroSlider->sort_order) }}" 
                                       min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="is_active" 
                                       name="is_active" 
                                       value="1" 
                                       {{ old('is_active', $heroSlider->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    เปิดใช้งาน
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.hero-sliders.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>กลับ
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>อัปเดต
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
