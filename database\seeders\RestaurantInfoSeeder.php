<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\RestaurantInfo;

class RestaurantInfoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        RestaurantInfo::create([
            'name' => 'ร้านก๋วยเตี๋ยวเรือเข้าท่า',
            'description' => 'ร้านก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม ด้วยน้ำซุปเข้มข้น เครื่องเทศครบเครื่อง สืบทอดตำรับโบราณมาอย่างยาวนาน อร่อยถูกปากทุกคน',
            'address' => '123 ถนนริมน้า เขตพระนคร กรุงเทพฯ 10200',
            'phone' => '02-123-4567',
            'mobile' => '************',
            'email' => '<EMAIL>',
            'website' => 'https://lastnoodle.com',
            'facebook' => 'https://facebook.com/lastnoodle',
            'line' => '@lastnoodle',
            'instagram' => '@lastnoodle_official',
            'open_time' => '08:00',
            'close_time' => '20:00',
            'open_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
            'latitude' => 13.7563,
            'longitude' => 100.5018,
            'is_active' => true,
        ]);
    }
}
