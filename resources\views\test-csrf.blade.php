<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>ทดสอบ CSRF Token</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">ทดสอบ CSRF Token และ Session</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>ข้อมูล Session:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <strong>Session ID:</strong><br>
                                        <code>{{ session()->getId() }}</code>
                                    </li>
                                    <li class="list-group-item">
                                        <strong>CSRF Token:</strong><br>
                                        <code>{{ csrf_token() }}</code>
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Session Driver:</strong><br>
                                        <code>{{ config('session.driver') }}</code>
                                    </li>
                                    <li class="list-group-item">
                                        <strong>App Key:</strong><br>
                                        <code>{{ config('app.key') ? 'Set' : 'Not Set' }}</code>
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Current Time:</strong><br>
                                        <code>{{ now() }}</code>
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>ทดสอบ Form:</h6>
                                <form method="POST" action="{{ route('test.csrf.post') }}">
                                    @csrf
                                    <div class="mb-3">
                                        <label for="test_data" class="form-label">ข้อมูลทดสอบ:</label>
                                        <input type="text" class="form-control" id="test_data" name="test_data" value="Hello World">
                                    </div>
                                    <button type="submit" class="btn btn-primary">ส่งข้อมูล</button>
                                </form>
                                
                                @if(session('test_result'))
                                    <div class="alert alert-success mt-3">
                                        <strong>ผลลัพธ์:</strong> {{ session('test_result') }}
                                    </div>
                                @endif
                                
                                @if($errors->any())
                                    <div class="alert alert-danger mt-3">
                                        <strong>Error:</strong>
                                        <ul class="mb-0">
                                            @foreach($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-12">
                                <h6>ลิงก์ทดสอบ:</h6>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('home') }}" class="btn btn-outline-primary">หน้าหลัก</a>
                                    <a href="{{ route('register') }}" class="btn btn-outline-success">สมัครสมาชิก</a>
                                    <a href="{{ route('login') }}" class="btn btn-outline-warning">เข้าสู่ระบบ</a>
                                    @auth
                                        @if(Auth::user()->isAdmin())
                                            <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-info">Admin</a>
                                        @endif
                                    @endauth
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
