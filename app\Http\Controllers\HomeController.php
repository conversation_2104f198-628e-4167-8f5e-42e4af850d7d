<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\MenuItem;
use App\Models\HeroSlider;
use App\Models\RestaurantInfo;

class HomeController extends Controller
{
    public function index()
    {
        $featuredMenus = MenuItem::with('category')
            ->active()
            ->featured()
            ->ordered()
            ->take(6)
            ->get();

        $categories = Category::active()->ordered()->get();
        $heroSliders = HeroSlider::active()->ordered()->get();
        $restaurantInfo = RestaurantInfo::getInfo();

        return view('home', compact('featuredMenus', 'categories', 'heroSliders', 'restaurantInfo'));
    }

    public function menu()
    {
        $categories = Category::active()->ordered()->get();
        $featuredMenus = MenuItem::active()->featured()->with('category')->ordered()->take(6)->get();
        $menuItems = MenuItem::active()->with('category')->ordered()->paginate(12);

        return view('menu.index', compact('categories', 'featuredMenus', 'menuItems'));
    }

    public function menuByCategory($categorySlug)
    {
        $categories = Category::active()->ordered()->get();
        $currentCategory = Category::where('slug', $categorySlug)->active()->firstOrFail();

        $featuredMenus = MenuItem::where('category_id', $currentCategory->id)
            ->active()
            ->featured()
            ->with('category')
            ->ordered()
            ->take(6)
            ->get();

        $menuItems = MenuItem::where('category_id', $currentCategory->id)
            ->active()
            ->with('category')
            ->ordered()
            ->paginate(12);

        return view('menu.index', compact('categories', 'currentCategory', 'featuredMenus', 'menuItems'));
    }

    public function about()
    {
        return view('about');
    }
}
