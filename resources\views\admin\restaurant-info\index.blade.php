@extends('layouts.admin')

@section('title', 'ข้อมูลร้าน - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 text-primary">
                    <i class="fas fa-store me-2"></i>ข้อมูลร้าน
                </h1>
                <a href="{{ route('admin.restaurant-info.edit') }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>แก้ไขข้อมูล
                </a>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Restaurant Info Cards -->
    <div class="row g-4">
        <!-- Basic Info -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>ข้อมูลพื้นฐาน
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>ชื่อร้าน:</strong>
                        <p class="mb-0">{{ $restaurantInfo->name ?: 'ยังไม่ได้กำหนด' }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>คำอธิบาย:</strong>
                        <p class="mb-0">{{ $restaurantInfo->description ?: 'ยังไม่ได้กำหนด' }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>ที่อยู่:</strong>
                        <p class="mb-0">{{ $restaurantInfo->address ?: 'ยังไม่ได้กำหนด' }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Info -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-phone me-2"></i>ข้อมูลติดต่อ
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>โทรศัพท์:</strong>
                        <p class="mb-0">{{ $restaurantInfo->phone ?: 'ยังไม่ได้กำหนด' }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>มือถือ:</strong>
                        <p class="mb-0">{{ $restaurantInfo->mobile ?: 'ยังไม่ได้กำหนด' }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>อีเมล:</strong>
                        <p class="mb-0">{{ $restaurantInfo->email ?: 'ยังไม่ได้กำหนด' }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>เว็บไซต์:</strong>
                        <p class="mb-0">
                            @if($restaurantInfo->website)
                                <a href="{{ $restaurantInfo->website }}" target="_blank">{{ $restaurantInfo->website }}</a>
                            @else
                                ยังไม่ได้กำหนด
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Media -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-share-alt me-2"></i>โซเชียลมีเดีย
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Facebook:</strong>
                        <p class="mb-0">{{ $restaurantInfo->facebook ?: 'ยังไม่ได้กำหนด' }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Line:</strong>
                        <p class="mb-0">{{ $restaurantInfo->line ?: 'ยังไม่ได้กำหนด' }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Instagram:</strong>
                        <p class="mb-0">{{ $restaurantInfo->instagram ?: 'ยังไม่ได้กำหนด' }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Opening Hours -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>เวลาเปิด-ปิด
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>เวลาเปิด-ปิด:</strong>
                        <p class="mb-0">{{ $restaurantInfo->formatted_opening_hours }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>วันเปิดทำการ:</strong>
                        <p class="mb-0">{{ $restaurantInfo->formatted_open_days }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Images -->
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-images me-2"></i>รูปภาพ
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>
                                <i class="fas fa-image me-2 text-primary"></i>โลโก้ร้าน
                                <span class="badge bg-primary ms-2">Navigation</span>
                            </h6>
                            @if($restaurantInfo->logo)
                                <div class="text-center p-3 bg-light rounded">
                                    <img src="{{ asset('storage/' . $restaurantInfo->logo) }}"
                                         alt="โลโก้ร้าน"
                                         class="img-thumbnail mb-2"
                                         style="max-height: 150px;">
                                    <div class="small text-success">
                                        <i class="fas fa-check-circle me-1"></i>กำลังใช้งาน
                                    </div>
                                    <div class="small text-muted">แสดงใน Navigation Bar</div>
                                </div>
                            @else
                                <div class="bg-light p-4 text-center text-muted rounded">
                                    <i class="fas fa-image fa-3x mb-2"></i>
                                    <p class="mb-1">ยังไม่มีโลโก้</p>
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>จะแสดงไอคอนเรือแทน
                                    </small>
                                </div>
                            @endif
                        </div>
                        <div class="col-md-4">
                            <h6>รูปปกร้าน</h6>
                            @if($restaurantInfo->cover_image)
                                <img src="{{ asset('storage/' . $restaurantInfo->cover_image) }}"
                                     alt="รูปปกร้าน"
                                     class="img-thumbnail"
                                     style="max-height: 200px;">
                            @else
                                <div class="bg-light p-4 text-center text-muted">
                                    <i class="fas fa-image fa-3x mb-2"></i>
                                    <p>ยังไม่มีรูปปก</p>
                                </div>
                            @endif
                        </div>
                        <div class="col-md-4">
                            <h6>รูปพื้นหลังหน้าหลัก</h6>
                            @if($restaurantInfo->background_image)
                                <img src="{{ asset('storage/' . $restaurantInfo->background_image) }}"
                                     alt="รูปพื้นหลังหน้าหลัก"
                                     class="img-thumbnail"
                                     style="max-height: 200px;">
                            @else
                                <div class="bg-light p-4 text-center text-muted">
                                    <i class="fas fa-image fa-3x mb-2"></i>
                                    <p>ยังไม่มีรูปพื้นหลัง</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Map -->
        @if($restaurantInfo->map_embed)
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>แผนที่
                    </h5>
                </div>
                <div class="card-body">
                    <div class="ratio ratio-16x9">
                        {!! $restaurantInfo->map_embed !!}
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

@push('styles')
<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
    border-bottom: none !important;
}
</style>
@endpush
@endsection
