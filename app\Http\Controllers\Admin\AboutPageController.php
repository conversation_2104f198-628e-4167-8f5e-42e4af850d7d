<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AboutPage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AboutPageController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $aboutPage = AboutPage::getInfo();
        return view('admin.about-page.index', compact('aboutPage'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit()
    {
        $aboutPage = AboutPage::getInfo();
        return view('admin.about-page.edit', compact('aboutPage'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'nullable|string',
            'our_story' => 'nullable|string',
            'main_content' => 'nullable|string',
            'our_mission' => 'nullable|string',
            'our_vision' => 'nullable|string',
            'hero_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'story_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'team_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_image_1' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_image_2' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_image_3' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $aboutPage = AboutPage::getInfo();
        $data = $request->all();

        // Handle image uploads
        $imageFields = ['hero_image', 'story_image', 'team_image', 'gallery_image_1', 'gallery_image_2', 'gallery_image_3'];

        foreach ($imageFields as $field) {
            if ($request->hasFile($field)) {
                // Delete old image if exists
                if ($aboutPage->$field) {
                    Storage::disk('public')->delete($aboutPage->$field);
                }
                // Store new image
                $data[$field] = $request->file($field)->store('about-page', 'public');
            }
        }

        // If no existing record, create new one
        if (!$aboutPage->exists) {
            AboutPage::create($data);
        } else {
            $aboutPage->update($data);
        }

        return redirect()->route('admin.about-page.index')
            ->with('success', 'ข้อมูลหน้าเกี่ยวกับเราถูกอัปเดตเรียบร้อยแล้ว');
    }
}
