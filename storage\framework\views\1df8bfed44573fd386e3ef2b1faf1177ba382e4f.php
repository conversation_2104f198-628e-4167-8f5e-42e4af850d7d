<?php $__env->startSection('title', $menuItem->name . ' - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-decoration-none">หน้าหลัก</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('menu.index')); ?>" class="text-decoration-none">เมนูอาหาร</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('menu.category', $menuItem->category->slug)); ?>" class="text-decoration-none"><?php echo e($menuItem->category->name); ?></a></li>
            <li class="breadcrumb-item active" aria-current="page"><?php echo e($menuItem->name); ?></li>
        </ol>
    </nav>

    <div class="row">
        <!-- Menu Item Details -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <?php if($menuItem->image): ?>
                    <img src="<?php echo e(asset('storage/' . $menuItem->image)); ?>" 
                         class="card-img-top" 
                         alt="<?php echo e($menuItem->name); ?>"
                         style="height: 400px; object-fit: cover;">
                <?php else: ?>
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 400px;">
                        <i class="fas fa-utensils fa-5x text-muted"></i>
                    </div>
                <?php endif; ?>
                
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h1 class="card-title h2 mb-0"><?php echo e($menuItem->name); ?></h1>
                        <span class="badge bg-primary fs-6 px-3 py-2"><?php echo e($menuItem->category->name); ?></span>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h3 class="text-primary h4">
                                <i class="fas fa-tag me-2"></i>
                                ราคา: <span class="text-success"><?php echo e(number_format($menuItem->price)); ?> บาท</span>
                            </h3>
                        </div>
                        <?php if($menuItem->is_featured): ?>
                            <div class="col-md-6 text-md-end">
                                <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                                    เมนูแนะนำ
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <?php if($menuItem->description): ?>
                        <div class="mb-4">
                            <h4 class="h5 mb-3">รายละเอียด</h4>
                            <p class="text-muted lh-lg"><?php echo e($menuItem->description); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="d-flex gap-3">
                        <a href="<?php echo e(route('menu.category', $menuItem->category->slug)); ?>" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>ดูเมนูในหมวดหมู่นี้
                        </a>
                        <a href="<?php echo e(route('menu.index')); ?>" 
                           class="btn btn-primary">
                            <i class="fas fa-utensils me-2"></i>ดูเมนูทั้งหมด
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Category Info -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>หมวดหมู่
                    </h5>
                </div>
                <div class="card-body">
                    <h6 class="text-primary"><?php echo e($menuItem->category->name); ?></h6>
                    <?php if($menuItem->category->description): ?>
                        <p class="text-muted small mb-0"><?php echo e($menuItem->category->description); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Related Items -->
            <?php if($relatedItems->count() > 0): ?>
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-utensils me-2"></i>เมนูที่เกี่ยวข้อง
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php $__currentLoopData = $relatedItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="d-flex align-items-center p-3 border-bottom">
                                <?php if($item->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $item->image)); ?>" 
                                         class="rounded me-3" 
                                         alt="<?php echo e($item->name); ?>"
                                         style="width: 60px; height: 60px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-utensils text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="<?php echo e(route('menu.show', $item->id)); ?>" 
                                           class="text-decoration-none text-dark">
                                            <?php echo e($item->name); ?>

                                        </a>
                                    </h6>
                                    <small class="text-success fw-bold"><?php echo e(number_format($item->price)); ?> บาท</small>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/menu/show.blade.php ENDPATH**/ ?>