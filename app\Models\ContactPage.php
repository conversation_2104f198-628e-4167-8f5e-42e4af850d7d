<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactPage extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'hero_image',
        'address',
        'phone',
        'mobile',
        'email',
        'line_id',
        'facebook',
        'instagram',
        'open_time',
        'close_time',
        'open_days',
        'special_hours',
        'map_embed',
        'latitude',
        'longitude',
        'directions',
        'location_image',
        'interior_image',
        'parking_image',
        'parking_info',
        'public_transport',
        'additional_info',
        'is_active'
    ];

    protected $casts = [
        'open_days' => 'array',
        'is_active' => 'boolean',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    // Get the contact page info (singleton pattern)
    public static function getInfo()
    {
        return static::where('is_active', true)->first() ?? new static();
    }

    // Get formatted open days
    public function getFormattedOpenDaysAttribute()
    {
        if (!$this->open_days) {
            return 'ทุกวัน';
        }

        $days = [
            'monday' => 'จันทร์',
            'tuesday' => 'อังคาร',
            'wednesday' => 'พุธ',
            'thursday' => 'พฤหัสบดี',
            'friday' => 'ศุกร์',
            'saturday' => 'เสาร์',
            'sunday' => 'อาทิตย์'
        ];

        $openDays = array_map(function($day) use ($days) {
            return $days[$day] ?? $day;
        }, $this->open_days);

        return implode(', ', $openDays);
    }

    // Get formatted opening hours
    public function getFormattedOpeningHoursAttribute()
    {
        if (!$this->open_time || !$this->close_time) {
            return 'ตามสอบถาม';
        }

        return $this->open_time . ' - ' . $this->close_time . ' น.';
    }
}
