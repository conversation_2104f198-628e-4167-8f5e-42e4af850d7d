@extends('layouts.app')

@section('title', $menuItem->name . ' - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-decoration-none">หน้าหลัก</a></li>
            <li class="breadcrumb-item"><a href="{{ route('menu.index') }}" class="text-decoration-none">เมนูอาหาร</a></li>
            <li class="breadcrumb-item"><a href="{{ route('menu.category', $menuItem->category->slug) }}" class="text-decoration-none">{{ $menuItem->category->name }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $menuItem->name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Menu Item Details -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                @if($menuItem->image)
                    <img src="{{ asset('storage/' . $menuItem->image) }}" 
                         class="card-img-top" 
                         alt="{{ $menuItem->name }}"
                         style="height: 400px; object-fit: cover;">
                @else
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 400px;">
                        <i class="fas fa-utensils fa-5x text-muted"></i>
                    </div>
                @endif
                
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h1 class="card-title h2 mb-0">{{ $menuItem->name }}</h1>
                        <span class="badge bg-primary fs-6 px-3 py-2">{{ $menuItem->category->name }}</span>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h3 class="text-primary h4">
                                <i class="fas fa-tag me-2"></i>
                                ราคา: <span class="text-success">{{ number_format($menuItem->price) }} บาท</span>
                            </h3>
                        </div>
                        @if($menuItem->is_featured)
                            <div class="col-md-6 text-md-end">
                                <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                                    <i class="fas fa-star me-1"></i>เมนูแนะนำ
                                </span>
                            </div>
                        @endif
                    </div>
                    
                    @if($menuItem->description)
                        <div class="mb-4">
                            <h4 class="h5 mb-3">รายละเอียด</h4>
                            <p class="text-muted lh-lg">{{ $menuItem->description }}</p>
                        </div>
                    @endif
                    
                    <div class="d-flex gap-3">
                        <a href="{{ route('menu.category', $menuItem->category->slug) }}" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>ดูเมนูในหมวดหมู่นี้
                        </a>
                        <a href="{{ route('menu.index') }}" 
                           class="btn btn-primary">
                            <i class="fas fa-utensils me-2"></i>ดูเมนูทั้งหมด
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Category Info -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>หมวดหมู่
                    </h5>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">{{ $menuItem->category->name }}</h6>
                    @if($menuItem->category->description)
                        <p class="text-muted small mb-0">{{ $menuItem->category->description }}</p>
                    @endif
                </div>
            </div>
            
            <!-- Related Items -->
            @if($relatedItems->count() > 0)
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-utensils me-2"></i>เมนูที่เกี่ยวข้อง
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        @foreach($relatedItems as $item)
                            <div class="d-flex align-items-center p-3 border-bottom">
                                @if($item->image)
                                    <img src="{{ asset('storage/' . $item->image) }}" 
                                         class="rounded me-3" 
                                         alt="{{ $item->name }}"
                                         style="width: 60px; height: 60px; object-fit: cover;">
                                @else
                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-utensils text-muted"></i>
                                    </div>
                                @endif
                                
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="{{ route('menu.show', $item->id) }}" 
                                           class="text-decoration-none text-dark">
                                            {{ $item->name }}
                                        </a>
                                    </h6>
                                    <small class="text-success fw-bold">{{ number_format($item->price) }} บาท</small>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
