@extends('layouts.admin')

@section('title', 'แก้ไขหน้าเกี่ยวกับเรา - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 text-primary">
                    <i class="fas fa-edit me-2"></i>แก้ไขหน้าเกี่ยวกับเรา
                </h1>
                <a href="{{ route('admin.about-page.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>กลับ
                </a>
            </div>
        </div>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ route('admin.about-page.update') }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <div class="row g-4">
            <!-- Basic Information -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>เนื้อหาหลัก
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="title" class="form-label">หัวข้อหน้า <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $aboutPage->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="content" class="form-label">เนื้อหาหลัก</label>
                            <textarea class="form-control @error('content') is-invalid @enderror" 
                                      id="content" name="content" rows="6">{{ old('content', $aboutPage->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Story Content -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-book me-2"></i>เรื่องราวและวิสัยทัศน์
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="our_story" class="form-label">เรื่องราวของเรา</label>
                            <textarea class="form-control @error('our_story') is-invalid @enderror" 
                                      id="our_story" name="our_story" rows="4">{{ old('our_story', $aboutPage->our_story) }}</textarea>
                            @error('our_story')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="our_mission" class="form-label">พันธกิจ</label>
                                    <textarea class="form-control @error('our_mission') is-invalid @enderror" 
                                              id="our_mission" name="our_mission" rows="4">{{ old('our_mission', $aboutPage->our_mission) }}</textarea>
                                    @error('our_mission')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="our_vision" class="form-label">วิสัยทัศน์</label>
                                    <textarea class="form-control @error('our_vision') is-invalid @enderror" 
                                              id="our_vision" name="our_vision" rows="4">{{ old('our_vision', $aboutPage->our_vision) }}</textarea>
                                    @error('our_vision')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Images -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-images me-2"></i>รูปภาพหลัก
                        </h5>
                        <small class="text-white-50">รูปภาพสำคัญที่แสดงในหน้าเกี่ยวกับเรา</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="hero_image" class="form-label">
                                        <i class="fas fa-image me-2 text-primary"></i>รูป Hero 
                                        <span class="badge bg-primary ms-2">หน้าหลัก</span>
                                    </label>
                                    <input type="file" class="form-control @error('hero_image') is-invalid @enderror" 
                                           id="hero_image" name="hero_image" accept="image/*">
                                    @error('hero_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">รูปหลักที่แสดงในหน้าเกี่ยวกับเรา</small>
                                    @if($aboutPage->hero_image)
                                        <div class="mt-3 p-3 bg-light rounded">
                                            <div class="text-center">
                                                <img src="{{ asset('storage/' . $aboutPage->hero_image) }}" 
                                                     alt="รูป Hero ปัจจุบัน" class="img-thumbnail mb-2" style="max-height: 120px;">
                                                <div class="small text-muted">รูป Hero ปัจจุบัน</div>
                                                <div class="small text-success">
                                                    <i class="fas fa-check-circle me-1"></i>กำลังใช้งาน
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="story_image" class="form-label">
                                        <i class="fas fa-image me-2 text-info"></i>รูปเรื่องราว
                                    </label>
                                    <input type="file" class="form-control @error('story_image') is-invalid @enderror" 
                                           id="story_image" name="story_image" accept="image/*">
                                    @error('story_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">รูปประกอบเรื่องราวของร้าน</small>
                                    @if($aboutPage->story_image)
                                        <div class="mt-3 p-3 bg-light rounded">
                                            <div class="text-center">
                                                <img src="{{ asset('storage/' . $aboutPage->story_image) }}" 
                                                     alt="รูปเรื่องราวปัจจุบัน" class="img-thumbnail mb-2" style="max-height: 120px;">
                                                <div class="small text-muted">รูปเรื่องราวปัจจุบัน</div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="team_image" class="form-label">
                                        <i class="fas fa-image me-2 text-success"></i>รูปทีมงาน
                                    </label>
                                    <input type="file" class="form-control @error('team_image') is-invalid @enderror" 
                                           id="team_image" name="team_image" accept="image/*">
                                    @error('team_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">รูปทีมงานของร้าน</small>
                                    @if($aboutPage->team_image)
                                        <div class="mt-3 p-3 bg-light rounded">
                                            <div class="text-center">
                                                <img src="{{ asset('storage/' . $aboutPage->team_image) }}" 
                                                     alt="รูปทีมงานปัจจุบัน" class="img-thumbnail mb-2" style="max-height: 120px;">
                                                <div class="small text-muted">รูปทีมงานปัจจุบัน</div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

            <!-- Gallery Images -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-photo-video me-2"></i>แกลเลอรี่
                        </h5>
                        <small class="text-white-50">รูปภาพเพิ่มเติมสำหรับแสดงในแกลเลอรี่</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="gallery_image_1" class="form-label">
                                        <i class="fas fa-image me-2 text-warning"></i>รูปแกลเลอรี่ 1
                                    </label>
                                    <input type="file" class="form-control @error('gallery_image_1') is-invalid @enderror"
                                           id="gallery_image_1" name="gallery_image_1" accept="image/*">
                                    @error('gallery_image_1')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if($aboutPage->gallery_image_1)
                                        <div class="mt-2">
                                            <img src="{{ asset('storage/' . $aboutPage->gallery_image_1) }}"
                                                 alt="แกลเลอรี่ 1 ปัจจุบัน" class="img-thumbnail" style="max-height: 100px;">
                                            <small class="text-muted d-block">แกลเลอรี่ 1 ปัจจุบัน</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="gallery_image_2" class="form-label">
                                        <i class="fas fa-image me-2 text-warning"></i>รูปแกลเลอรี่ 2
                                    </label>
                                    <input type="file" class="form-control @error('gallery_image_2') is-invalid @enderror"
                                           id="gallery_image_2" name="gallery_image_2" accept="image/*">
                                    @error('gallery_image_2')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if($aboutPage->gallery_image_2)
                                        <div class="mt-2">
                                            <img src="{{ asset('storage/' . $aboutPage->gallery_image_2) }}"
                                                 alt="แกลเลอรี่ 2 ปัจจุบัน" class="img-thumbnail" style="max-height: 100px;">
                                            <small class="text-muted d-block">แกลเลอรี่ 2 ปัจจุบัน</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="gallery_image_3" class="form-label">
                                        <i class="fas fa-image me-2 text-warning"></i>รูปแกลเลอรี่ 3
                                    </label>
                                    <input type="file" class="form-control @error('gallery_image_3') is-invalid @enderror"
                                           id="gallery_image_3" name="gallery_image_3" accept="image/*">
                                    @error('gallery_image_3')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if($aboutPage->gallery_image_3)
                                        <div class="mt-2">
                                            <img src="{{ asset('storage/' . $aboutPage->gallery_image_3) }}"
                                                 alt="แกลเลอรี่ 3 ปัจจุบัน" class="img-thumbnail" style="max-height: 100px;">
                                            <small class="text-muted d-block">แกลเลอรี่ 3 ปัจจุบัน</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-end gap-2">
                    <a href="{{ route('admin.about-page.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>ยกเลิก
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>บันทึกข้อมูล
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

@push('styles')
<style>
.card {
    transition: all 0.3s ease;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
    border-bottom: none !important;
}
</style>
@endpush
@endsection
