<?php $__env->startSection('title', 'เมนูอาหาร - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section position-relative overflow-hidden" style="min-height: 40vh; background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--boat-blue) 100%);">
    <div class="container position-relative h-100">
        <div class="row align-items-center h-100">
            <div class="col-lg-8 mx-auto text-center">
                <div class="fade-in-up">
                    <h1 class="display-3 fw-bold mb-4 text-white text-shadow">
                        <i class="fas fa-utensils me-3 text-warning pulse-animation"></i>
                        เมนูอาหาร
                    </h1>
                    <p class="lead mb-4 text-white fs-4">
                        ก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม พร้อมเครื่องเทศครบเครื่อง<br>
                        <small class="d-block mt-2 opacity-85">ค้นหาเมนูโปรดของคุณได้ที่นี่</small>
                    </p>
                    <div class="d-inline-block">
                        <div class="bg-warning" style="height: 3px; width: 100px; border-radius: 2px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Decorative Wave -->
    <div class="position-absolute bottom-0 start-0 w-100">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" style="height: 60px; width: 100%;">
            <path d="M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z" fill="white"></path>
        </svg>
    </div>
</section>

<!-- Categories Filter -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h3 class="fw-bold text-primary mb-3">
                        <i class="fas fa-filter me-2"></i>เลือกหมวดหมู่
                    </h3>
                    <p class="text-muted">กรองเมนูตามหมวดหมู่ที่คุณสนใจ</p>
                </div>

                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="<?php echo e(route('menu.index')); ?>"
                       class="btn <?php echo e(!request('category') ? 'btn-primary' : 'btn-outline-primary'); ?> rounded-pill px-4 py-2 shadow-sm">
                        <i class="fas fa-th-large me-2"></i>ทั้งหมด
                        <span class="badge bg-light text-dark ms-2"><?php echo e($allMenusCount ?? 0); ?></span>
                    </a>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e(route('menu.category', $category->slug)); ?>"
                           class="btn <?php echo e(request('category') == $category->slug ? 'btn-primary' : 'btn-outline-primary'); ?> rounded-pill px-4 py-2 shadow-sm position-relative">
                            <i class="fas fa-utensils me-2"></i><?php echo e($category->name); ?>

                            <span class="badge bg-light text-dark ms-2"><?php echo e($category->menuItems->count()); ?></span>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Menu Section -->
<?php if($featuredMenus->count() > 0): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold">
                    เมนูแนะนำ
                </h2>
                <p class="lead text-muted">เมนูพิเศษที่ลูกค้าชื่นชอบมากที่สุด</p>
            </div>
        </div>
        
        <div class="row g-4">
            <?php $__currentLoopData = $featuredMenus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-lg menu-card" style="animation-delay: <?php echo e($index * 0.1); ?>s;">
                        <!-- Featured Badge -->
                        <div class="position-absolute top-0 end-0 z-index-1">
                            <div class="bg-warning text-dark px-3 py-2 rounded-bottom-start">
                                <i class="fas fa-crown me-1"></i>แนะนำ
                            </div>
                        </div>
                        
                        <?php if($menu->image): ?>
                            <div class="position-relative overflow-hidden">
                                <img src="<?php echo e(asset('storage/' . $menu->image)); ?>" 
                                     class="card-img-top menu-image" 
                                     alt="<?php echo e($menu->name); ?>" 
                                     style="height: 250px; object-fit: cover;">
                            </div>
                        <?php else: ?>
                            <div class="card-img-top bg-gradient d-flex align-items-center justify-content-center" 
                                 style="height: 250px; background: linear-gradient(135deg, var(--light-brown), var(--cream-color));">
                                <i class="fas fa-utensils fa-4x text-primary opacity-50"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="card-body d-flex flex-column">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h5 class="card-title text-primary fw-bold mb-0"><?php echo e($menu->name); ?></h5>
                                <span class="badge bg-primary rounded-pill"><?php echo e($menu->category->name); ?></span>
                            </div>
                            
                            <p class="card-text text-muted flex-grow-1 mb-3"><?php echo e($menu->description); ?></p>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="price-tag">
                                    <span class="h4 text-primary fw-bold mb-0"><?php echo e($menu->formatted_price); ?></span>
                                </div>
                                <?php if(auth()->guard()->check()): ?>
                                    <button class="btn btn-outline-primary btn-sm rounded-pill px-3">
                                        <i class="fas fa-heart me-1"></i>ชอบ
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- All Menu Section -->
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold">
                    <i class="fas fa-list me-3 text-primary"></i>
                    <?php if(request('category')): ?>
                        <?php echo e($currentCategory->name ?? 'เมนูอาหาร'); ?>

                    <?php else: ?>
                        เมนูอาหารทั้งหมด
                    <?php endif; ?>
                </h2>
            </div>
        </div>
        
        <?php if($menuItems->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-3 col-md-6">
                        <div class="card h-100 border-0 shadow menu-card-small" style="animation-delay: <?php echo e($index * 0.05); ?>s;">
                            <?php if($menu->image): ?>
                                <div class="position-relative overflow-hidden">
                                    <img src="<?php echo e(asset('storage/' . $menu->image)); ?>" 
                                         class="card-img-top menu-image-small" 
                                         alt="<?php echo e($menu->name); ?>" 
                                         style="height: 200px; object-fit: cover;">
                                    <?php if($menu->is_featured): ?>
                                        <div class="position-absolute top-0 end-0 p-2">
                                            <span class="badge bg-warning text-dark">
                                                แนะนำ
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                     style="height: 200px;">
                                    <i class="fas fa-utensils fa-3x text-muted opacity-50"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title text-primary fw-bold mb-2"><?php echo e($menu->name); ?></h6>
                                <p class="card-text text-muted small flex-grow-1 mb-3">
                                    <?php echo e(Str::limit($menu->description, 80)); ?>

                                </p>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-primary fw-bold"><?php echo e($menu->formatted_price); ?></span>
                                    <small class="text-muted"><?php echo e($menu->category->name); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            
            <!-- Pagination -->
            <?php if($menuItems->hasPages()): ?>
                <div class="row mt-5">
                    <div class="col-12 d-flex justify-content-center">
                        <?php echo e($menuItems->links()); ?>

                    </div>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-utensils fa-5x text-muted opacity-50 mb-4"></i>
                <h4 class="text-muted mb-3">ไม่พบเมนูอาหาร</h4>
                <p class="text-muted">
                    <?php if(request('category')): ?>
                        ไม่มีเมนูในหมวดหมู่นี้ในขณะนี้
                    <?php else: ?>
                        ยังไม่มีเมนูอาหารในระบบ
                    <?php endif; ?>
                </p>
                <a href="<?php echo e(route('menu.index')); ?>" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>ดูเมนูทั้งหมด
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<style>
.menu-card {
    transition: all 0.4s ease;
    transform: translateY(0);
}

.menu-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.menu-card:hover .menu-image {
    transform: scale(1.05);
}

.menu-image {
    transition: transform 0.4s ease;
}

.menu-card-small {
    transition: all 0.3s ease;
}

.menu-card-small:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.menu-card-small:hover .menu-image-small {
    transform: scale(1.05);
}

.menu-image-small {
    transition: transform 0.3s ease;
}

.price-tag {
    position: relative;
}

.price-tag::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--gold-color));
    border-radius: 1px;
}

.z-index-1 {
    z-index: 1;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.text-white-75 {
    color: rgba(255,255,255,0.9) !important;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/menu/index.blade.php ENDPATH**/ ?>