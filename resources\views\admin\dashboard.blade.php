@extends('layouts.admin')

@section('title', 'หน้าจัดการระบบ - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 text-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>หน้าจัดการระบบ
                </h1>
                <div class="text-muted">
                    <i class="fas fa-user me-1"></i>{{ Auth::user()->name }}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4 g-4">
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-lg h-100 stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white position-relative overflow-hidden">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 fw-bold">{{ $stats['categories'] }}</h2>
                            <p class="mb-0 opacity-75">หมวดหมู่อาหาร</p>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-list fa-3x opacity-50"></i>
                        </div>
                    </div>
                    <div class="position-absolute bottom-0 end-0 p-3">
                        <i class="fas fa-arrow-up text-success me-1"></i>
                        <small>พร้อมใช้งาน</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-lg h-100 stat-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="card-body text-white position-relative overflow-hidden">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 fw-bold">{{ $stats['menu_items'] }}</h2>
                            <p class="mb-0 opacity-75">เมนูทั้งหมด</p>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-utensils fa-3x opacity-50"></i>
                        </div>
                    </div>
                    <div class="position-absolute bottom-0 end-0 p-3">
                        <i class="fas fa-chart-line text-success me-1"></i>
                        <small>เพิ่มขึ้น</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-lg h-100 stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white position-relative overflow-hidden">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 fw-bold">{{ $stats['active_menu_items'] }}</h2>
                            <p class="mb-0 opacity-75">เมนูที่เปิดใช้งาน</p>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-check-circle fa-3x opacity-50"></i>
                        </div>
                    </div>
                    <div class="position-absolute bottom-0 end-0 p-3">
                        <i class="fas fa-eye text-info me-1"></i>
                        <small>กำลังแสดง</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-lg h-100 stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="card-body text-white position-relative overflow-hidden">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 fw-bold">{{ $stats['featured_menu_items'] }}</h2>
                            <p class="mb-0 opacity-75">เมนูแนะนำ</p>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-star fa-3x opacity-50"></i>
                        </div>
                    </div>
                    <div class="position-absolute bottom-0 end-0 p-3">
                        <i class="fas fa-heart text-warning me-1"></i>
                        <small>ยอดนิยม</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- News Statistics -->
    <div class="row mb-4 g-4">
        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow-lg h-100 stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white position-relative overflow-hidden">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 fw-bold">{{ $stats['news'] }}</h2>
                            <p class="mb-0 opacity-75">ข่าวสารทั้งหมด</p>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-newspaper fa-3x opacity-50"></i>
                        </div>
                    </div>
                    <div class="position-absolute bottom-0 end-0 p-3">
                        <i class="fas fa-plus text-info me-1"></i>
                        <small>รวมทั้งหมด</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow-lg h-100 stat-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="card-body text-white position-relative overflow-hidden">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 fw-bold">{{ $stats['published_news'] }}</h2>
                            <p class="mb-0 opacity-75">ข่าวสารที่เผยแพร่</p>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-eye fa-3x opacity-50"></i>
                        </div>
                    </div>
                    <div class="position-absolute bottom-0 end-0 p-3">
                        <i class="fas fa-check text-success me-1"></i>
                        <small>กำลังแสดง</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow-lg h-100 stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="card-body text-white position-relative overflow-hidden">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 fw-bold">{{ $stats['featured_news'] }}</h2>
                            <p class="mb-0 opacity-75">ข่าวสารแนะนำ</p>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-star fa-3x opacity-50"></i>
                        </div>
                    </div>
                    <div class="position-absolute bottom-0 end-0 p-3">
                        <i class="fas fa-fire text-warning me-1"></i>
                        <small>พิเศษ</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>การจัดการด่วน
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-list fa-2x mb-2"></i>
                                <span>จัดการหมวดหมู่</span>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.menu-items.index') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-utensils fa-2x mb-2"></i>
                                <span>จัดการเมนูอาหาร</span>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.menu-items.create') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-plus fa-2x mb-2"></i>
                                <span>เพิ่มเมนูใหม่</span>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.news.index') }}" class="btn btn-outline-danger w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-newspaper fa-2x mb-2"></i>
                                <span>จัดการข่าวสาร</span>
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.restaurant-info.index') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-store fa-2x mb-2"></i>
                                <span>ข้อมูลร้าน</span>
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('home') }}" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-eye fa-2x mb-2"></i>
                                <span>ดูหน้าเว็บไซต์</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Menu Items -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>เมนูที่เพิ่มล่าสุด
                    </h5>
                    <a href="{{ route('admin.menu-items.index') }}" class="btn btn-sm btn-outline-primary">
                        ดูทั้งหมด
                    </a>
                </div>
                <div class="card-body">
                    @if($recentMenuItems->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ชื่อเมนู</th>
                                        <th>หมวดหมู่</th>
                                        <th>ราคา</th>
                                        <th>สถานะ</th>
                                        <th>วันที่เพิ่ม</th>
                                        <th>การจัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentMenuItems as $item)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($item->image)
                                                        <img src="{{ asset('storage/' . $item->image) }}" 
                                                             alt="{{ $item->name }}" 
                                                             class="rounded me-2" 
                                                             style="width: 40px; height: 40px; object-fit: cover;">
                                                    @else
                                                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                                             style="width: 40px; height: 40px;">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    @endif
                                                    <span>{{ $item->name }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ $item->category->name }}</span>
                                            </td>
                                            <td>{{ $item->formatted_price }}</td>
                                            <td>
                                                @if($item->is_active)
                                                    <span class="badge bg-success">เปิดใช้งาน</span>
                                                @else
                                                    <span class="badge bg-danger">ปิดใช้งาน</span>
                                                @endif
                                                
                                                @if($item->is_featured)
                                                    <span class="badge bg-warning text-dark">แนะนำ</span>
                                                @endif
                                            </td>
                                            <td>{{ $item->created_at->format('d/m/Y H:i') }}</td>
                                            <td>
                                                <a href="{{ route('admin.menu-items.edit', $item) }}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                            <p class="text-muted">ยังไม่มีเมนูอาหาร</p>
                            <a href="{{ route('admin.menu-items.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>เพิ่มเมนูแรก
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.stat-card {
    transition: all 0.3s ease;
    transform: translateY(0);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2) !important;
}

.stat-icon {
    transition: transform 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
}

.quick-action-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.quick-action-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(139, 69, 19, 0.05);
    transform: scale(1.01);
}

.breadcrumb {
    background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,249,250,0.9));
    backdrop-filter: blur(10px);
    border-radius: 25px;
    border: 1px solid rgba(139, 69, 19, 0.1);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
    border-bottom: none !important;
}

.btn-group .btn {
    border-radius: 8px !important;
    margin: 0 2px;
}

.badge {
    border-radius: 20px;
    padding: 0.5em 1em;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}
</style>
@endpush
@endsection
