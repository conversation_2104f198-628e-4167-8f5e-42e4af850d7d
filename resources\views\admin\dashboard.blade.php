@extends('layouts.admin')

@section('title', 'หน้าจัดการระบบ - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container-fluid py-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-header p-4 rounded-4 shadow-sm">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h2 mb-2 text-white">
                            <span class="icon-boat-noodle icon-boat-noodle-2x me-3" style="filter: brightness(0) invert(1);"></span>
                            ยินดีต้อนรับสู่ระบบจัดการร้าน
                        </h1>
                        <p class="mb-0 text-white-50 fs-5">จัดการเมนู ข่าวสาร และข้อมูลร้านของคุณได้ง่ายๆ</p>
                    </div>
                    <div class="text-end">
                        <div class="bg-white bg-opacity-20 rounded-3 p-3">
                            <div class="text-white">
                                <i class="fas fa-user-shield fs-4 mb-2 d-block"></i>
                                <div class="fw-bold">{{ Auth::user()->name }}</div>
                                <small class="opacity-75">ผู้ดูแลระบบ</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="mb-4 text-dark">
                <i class="fas fa-chart-bar me-2 text-primary"></i>สถิติภาพรวม
            </h3>
        </div>
    </div>

    <div class="row mb-5 g-4">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-icon bg-primary">
                        <i class="fas fa-th-large"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number">{{ $stats['categories'] }}</h3>
                        <p class="stats-label">หมวดหมู่อาหาร</p>
                        <div class="stats-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">พร้อมใช้งาน</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-icon bg-success">
                        <span class="icon-boat-noodle"></span>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number">{{ $stats['menu_items'] }}</h3>
                        <p class="stats-label">เมนูทั้งหมด</p>
                        <div class="stats-trend">
                            <i class="fas fa-chart-line text-success"></i>
                            <span class="text-success">เพิ่มขึ้น</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-icon bg-info">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number">{{ $stats['active_menu_items'] }}</h3>
                        <p class="stats-label">เมนูที่เปิดใช้งาน</p>
                        <div class="stats-trend">
                            <i class="fas fa-eye text-info"></i>
                            <span class="text-info">กำลังแสดง</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-icon bg-warning">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number">{{ $stats['featured_menu_items'] }}</h3>
                        <p class="stats-label">เมนูแนะนำ</p>
                        <div class="stats-trend">
                            <i class="fas fa-heart text-warning"></i>
                            <span class="text-warning">ยอดนิยม</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="mb-4 text-dark">
                <i class="fas fa-bolt me-2 text-primary"></i>การจัดการด่วน
            </h3>
        </div>
    </div>

    <div class="row mb-5 g-4">
        <div class="col-lg-2 col-md-4 col-sm-6">
            <a href="{{ route('admin.categories.index') }}" class="quick-action-link">
                <div class="quick-action-card">
                    <div class="quick-action-icon bg-primary">
                        <i class="fas fa-th-large"></i>
                    </div>
                    <h6 class="quick-action-title">จัดการหมวดหมู่</h6>
                    <p class="quick-action-desc">เพิ่ม แก้ไข หมวดหมู่อาหาร</p>
                </div>
            </a>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <a href="{{ route('admin.menu-items.index') }}" class="quick-action-link">
                <div class="quick-action-card">
                    <div class="quick-action-icon bg-success">
                        <span class="icon-boat-noodle"></span>
                    </div>
                    <h6 class="quick-action-title">จัดการเมนูอาหาร</h6>
                    <p class="quick-action-desc">ดู แก้ไข เมนูทั้งหมด</p>
                </div>
            </a>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <a href="{{ route('admin.menu-items.create') }}" class="quick-action-link">
                <div class="quick-action-card">
                    <div class="quick-action-icon bg-info">
                        <i class="fas fa-plus"></i>
                    </div>
                    <h6 class="quick-action-title">เพิ่มเมนูใหม่</h6>
                    <p class="quick-action-desc">สร้างเมนูอาหารใหม่</p>
                </div>
            </a>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <a href="{{ route('admin.news.index') }}" class="quick-action-link">
                <div class="quick-action-card">
                    <div class="quick-action-icon bg-danger">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <h6 class="quick-action-title">จัดการข่าวสาร</h6>
                    <p class="quick-action-desc">เพิ่ม แก้ไข ข่าวสาร</p>
                </div>
            </a>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <a href="{{ route('admin.restaurant-info.index') }}" class="quick-action-link">
                <div class="quick-action-card">
                    <div class="quick-action-icon bg-warning">
                        <i class="fas fa-store"></i>
                    </div>
                    <h6 class="quick-action-title">ข้อมูลร้าน</h6>
                    <p class="quick-action-desc">แก้ไขข้อมูลร้าน</p>
                </div>
            </a>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <a href="{{ route('home') }}" class="quick-action-link" target="_blank">
                <div class="quick-action-card">
                    <div class="quick-action-icon bg-secondary">
                        <i class="fas fa-external-link-alt"></i>
                    </div>
                    <h6 class="quick-action-title">ดูหน้าเว็บไซต์</h6>
                    <p class="quick-action-desc">เปิดหน้าเว็บไซต์</p>
                </div>
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="mb-4 text-dark">
                <i class="fas fa-clock me-2 text-primary"></i>กิจกรรมล่าสุด
            </h3>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="recent-activity-card">
                <div class="recent-activity-header">
                    <h5 class="mb-0 text-white">
                        <span class="icon-boat-noodle me-2"></span>เมนูที่เพิ่มล่าสุด
                    </h5>
                    <a href="{{ route('admin.menu-items.index') }}" class="btn btn-sm btn-light">
                        <i class="fas fa-arrow-right me-1"></i>ดูทั้งหมด
                    </a>
                </div>
                <div class="recent-activity-body">
                    @if($recentMenuItems->count() > 0)
                        <div class="recent-items-grid">
                            @foreach($recentMenuItems as $item)
                                <div class="recent-item-card">
                                    <div class="recent-item-image">
                                        @if($item->image)
                                            <img src="{{ asset('storage/' . $item->image) }}" alt="{{ $item->name }}">
                                        @else
                                            <div class="no-image">
                                                <span class="icon-boat-noodle"></span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="recent-item-content">
                                        <h6 class="recent-item-title">{{ $item->name }}</h6>
                                        <div class="recent-item-meta">
                                            <span class="badge bg-primary">{{ $item->category->name }}</span>
                                            <span class="price">{{ $item->formatted_price }}</span>
                                        </div>
                                        <div class="recent-item-status">
                                            @if($item->is_active)
                                                <span class="status-badge active">เปิดใช้งาน</span>
                                            @else
                                                <span class="status-badge inactive">ปิดใช้งาน</span>
                                            @endif
                                            @if($item->is_featured)
                                                <span class="status-badge featured">แนะนำ</span>
                                            @endif
                                        </div>
                                        <div class="recent-item-actions">
                                            <a href="{{ route('admin.menu-items.edit', $item) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i> แก้ไข
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                    @else
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <span class="icon-boat-noodle icon-boat-noodle-3x"></span>
                            </div>
                            <h5 class="empty-state-title">ยังไม่มีเมนูอาหาร</h5>
                            <p class="empty-state-desc">เริ่มต้นสร้างเมนูอาหารแรกของคุณ</p>
                            <a href="{{ route('admin.menu-items.create') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus me-2"></i>เพิ่มเมนูแรก
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* Welcome Header */
.welcome-header {
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
    border: none;
    position: relative;
    overflow: hidden;
}

.welcome-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1.5" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="white" opacity="0.1"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Statistics Cards */
.stats-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
    border: none;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.stats-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.stats-card-body {
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stats-icon {
    width: 70px;
    height: 70px;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.stats-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.2), transparent);
    border-radius: inherit;
}

.stats-content {
    flex: 1;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    color: #2c3e50;
    line-height: 1;
}

.stats-label {
    font-size: 1rem;
    color: #6c757d;
    margin: 0.5rem 0;
    font-weight: 500;
}

.stats-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Quick Action Cards */
.quick-action-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.quick-action-card {
    background: white;
    border-radius: 16px;
    padding: 2rem 1.5rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #8B4513, #D2691E);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.quick-action-card:hover::before {
    transform: scaleX(1);
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    border-color: rgba(139, 69, 19, 0.2);
}

.quick-action-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.quick-action-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.2), transparent);
    border-radius: inherit;
}

.quick-action-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.quick-action-desc {
    font-size: 0.875rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.4;
}

/* Recent Activity */
.recent-activity-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
    overflow: hidden;
}

.recent-activity-header {
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: between;
    align-items: center;
}

.recent-activity-body {
    padding: 2rem;
}

.recent-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.recent-item-card {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.recent-item-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    border-color: rgba(139, 69, 19, 0.2);
}

.recent-item-image {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 1rem;
    position: relative;
}

.recent-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #6c757d;
}

.recent-item-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.recent-item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.price {
    font-size: 1.1rem;
    font-weight: 600;
    color: #8B4513;
}

.recent-item-status {
    margin-bottom: 1rem;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.featured {
    background: #fff3cd;
    color: #856404;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-state-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1.5rem;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 0.75rem;
}

.empty-state-desc {
    color: #adb5bd;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-header {
        text-align: center;
    }

    .welcome-header .d-flex {
        flex-direction: column;
        gap: 1.5rem;
    }

    .stats-card-body {
        padding: 1.5rem;
        gap: 1rem;
    }

    .stats-number {
        font-size: 2rem;
    }

    .quick-action-card {
        padding: 1.5rem 1rem;
    }

    .recent-items-grid {
        grid-template-columns: 1fr;
    }
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}
</style>
@endpush
@endsection
