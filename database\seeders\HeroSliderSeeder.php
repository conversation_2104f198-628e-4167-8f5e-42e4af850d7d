<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class HeroSliderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $sliders = [
            [
                'title' => 'ร้านก๋วยเตี๋ยวเรือเข้าท่า',
                'subtitle' => 'ก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม',
                'description' => 'ด้วยน้ำซุปเข้มข้น เครื่องเทศครบเครื่อง สืบทอดตำรับโบราณมาอย่างยาวนาน อร่อยถูกปากทุกคน',
                'image' => 'hero-sliders/slide1.jpg',
                'button_text' => 'ดูเมนูอาหาร',
                'button_link' => '/menu',
                'button_color' => 'primary',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'เมนูแนะนำประจำร้าน',
                'subtitle' => 'รสชาติเด็ด ต้องลอง',
                'description' => 'เมนูพิเศษที่ลูกค้าชื่นชอบมากที่สุด ด้วยส่วนผสมคุณภาพ และการปรุงรสที่ลงตัว',
                'image' => 'hero-sliders/slide2.jpg',
                'button_text' => 'ดูเมนูแนะนำ',
                'button_link' => '/menu',
                'button_color' => 'success',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'title' => 'บรรยากาศดี ใจกลางเมือง',
                'subtitle' => 'สะดวก สบาย ใกล้ทุกที่',
                'description' => 'ตั้งอยู่ในทำเลที่สะดวกต่อการเดินทาง บรรยากาศร่มรื่น เหมาะสำหรับทุกโอกาส',
                'image' => 'hero-sliders/slide3.jpg',
                'button_text' => 'ดูที่ตั้ง',
                'button_link' => '/about',
                'button_color' => 'info',
                'sort_order' => 3,
                'is_active' => true,
            ],
        ];

        foreach ($sliders as $slider) {
            \App\Models\HeroSlider::create($slider);
        }
    }
}
