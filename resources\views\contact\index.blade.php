@extends('layouts.app')

@section('title', 'ติดต่อเรา - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<!-- Hero Section with Background Image -->
@if($contactPage->hero_image)
<section class="hero-section position-relative" style="background: linear-gradient(rgba(139, 69, 19, 0.8), rgba(139, 69, 19, 0.8)), url('{{ asset('storage/' . $contactPage->hero_image) }}'); background-size: cover; background-position: center; min-height: 60vh;">
@else
<section class="py-5 bg-primary text-white">
@endif
    <div class="container">
        <div class="row align-items-center" style="min-height: 50vh;">
            <div class="col-lg-8 mx-auto text-center text-white">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-phone me-3"></i>{{ $contactPage->title ?? 'ติดต่อเรา' }}
                </h1>
                <p class="lead fs-5 mb-4">
                    {{ $contactPage->description ?? 'ติดต่อสอบถามข้อมูลเพิ่มเติม หรือจองโต๊ะล่วงหน้า' }}
                </p>
                @if($contactPage->hero_image)
                <div class="mt-4">
                    <div class="d-inline-block bg-white bg-opacity-25 rounded-pill px-4 py-2">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <span>พร้อมให้บริการทุกวัน</span>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Contact Information Section -->
<section class="py-5">
    <div class="container">
        <div class="row g-4">
            <!-- Contact Details -->
            <div class="col-lg-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-address-book me-2"></i>ข้อมูลติดต่อ
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Address -->
                        @if($contactPage->address || $restaurantInfo->address)
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-map-marker-alt fa-2x text-primary"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">ที่อยู่ร้าน</h6>
                                    <p class="mb-0 text-muted">
                                        {{ $contactPage->address ?? $restaurantInfo->address }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Phone -->
                        @if($contactPage->phone || $restaurantInfo->phone)
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-phone fa-2x text-success"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">โทรศัพท์</h6>
                                    <p class="mb-0">
                                        <a href="tel:{{ $contactPage->phone ?? $restaurantInfo->phone }}" class="text-decoration-none">
                                            {{ $contactPage->phone ?? $restaurantInfo->phone }}
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Mobile -->
                        @if($contactPage->mobile || $restaurantInfo->mobile)
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-mobile-alt fa-2x text-info"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">มือถือ</h6>
                                    <p class="mb-0">
                                        <a href="tel:{{ $contactPage->mobile ?? $restaurantInfo->mobile }}" class="text-decoration-none">
                                            {{ $contactPage->mobile ?? $restaurantInfo->mobile }}
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Email -->
                        @if($contactPage->email || $restaurantInfo->email)
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-envelope fa-2x text-warning"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">อีเมล</h6>
                                    <p class="mb-0">
                                        <a href="mailto:{{ $contactPage->email ?? $restaurantInfo->email }}" class="text-decoration-none">
                                            {{ $contactPage->email ?? $restaurantInfo->email }}
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Opening Hours -->
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-clock fa-2x text-primary"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">เวลาเปิด-ปิด</h6>
                                    <p class="mb-1">
                                        {{ $contactPage->formatted_opening_hours ?? $restaurantInfo->formatted_opening_hours ?? 'เปิดทุกวัน 08:00 - 20:00 น.' }}
                                    </p>
                                    @if($contactPage->formatted_open_days)
                                    <small class="text-muted">{{ $contactPage->formatted_open_days }}</small>
                                    @endif
                                    @if($contactPage->special_hours)
                                    <div class="mt-2">
                                        <small class="text-info">
                                            <i class="fas fa-info-circle me-1"></i>{{ $contactPage->special_hours }}
                                        </small>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social Media & Additional Info -->
            <div class="col-lg-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-share-alt me-2"></i>ช่องทางติดต่ออื่นๆ
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Line -->
                        @if($contactPage->line_id || $restaurantInfo->line)
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fab fa-line fa-2x text-success"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">Line ID</h6>
                                    <p class="mb-0">
                                        <a href="https://line.me/ti/p/{{ $contactPage->line_id ?? $restaurantInfo->line }}" 
                                           target="_blank" class="text-decoration-none">
                                            {{ $contactPage->line_id ?? $restaurantInfo->line }}
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Facebook -->
                        @if($contactPage->facebook || $restaurantInfo->facebook)
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fab fa-facebook fa-2x text-primary"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">Facebook</h6>
                                    <p class="mb-0">
                                        <a href="{{ $contactPage->facebook ?? $restaurantInfo->facebook }}" 
                                           target="_blank" class="text-decoration-none">
                                            Facebook Page
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Instagram -->
                        @if($contactPage->instagram || $restaurantInfo->instagram)
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fab fa-instagram fa-2x text-danger"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">Instagram</h6>
                                    <p class="mb-0">
                                        <a href="{{ $contactPage->instagram ?? $restaurantInfo->instagram }}" 
                                           target="_blank" class="text-decoration-none">
                                            Instagram Page
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Additional Info -->
                        @if($contactPage->additional_info)
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon">
                                    <i class="fas fa-info-circle fa-2x text-info"></i>
                                </div>
                                <div class="contact-info ms-3">
                                    <h6 class="fw-bold mb-1">ข้อมูลเพิ่มเติม</h6>
                                    <p class="mb-0 text-muted">
                                        {!! nl2br(e($contactPage->additional_info)) !!}
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Quick Contact Buttons -->
                        <div class="mt-4">
                            <h6 class="fw-bold mb-3">ติดต่อด่วน</h6>
                            <div class="d-flex flex-wrap gap-2">
                                @if($contactPage->phone || $restaurantInfo->phone)
                                <a href="tel:{{ $contactPage->phone ?? $restaurantInfo->phone }}" 
                                   class="btn btn-success btn-sm">
                                    <i class="fas fa-phone me-1"></i>โทรเลย
                                </a>
                                @endif
                                
                                @if($contactPage->line_id || $restaurantInfo->line)
                                <a href="https://line.me/ti/p/{{ $contactPage->line_id ?? $restaurantInfo->line }}" 
                                   target="_blank" class="btn btn-success btn-sm">
                                    <i class="fab fa-line me-1"></i>Line
                                </a>
                                @endif
                                
                                @if($contactPage->facebook || $restaurantInfo->facebook)
                                <a href="{{ $contactPage->facebook ?? $restaurantInfo->facebook }}" 
                                   target="_blank" class="btn btn-primary btn-sm">
                                    <i class="fab fa-facebook me-1"></i>Facebook
                                </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Location Images Section -->
@if($contactPage->location_image || $contactPage->interior_image || $contactPage->parking_image)
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center text-primary mb-5">
            <i class="fas fa-images me-2"></i>รูปภาพร้าน
        </h2>
        <div class="row g-4">
            @if($contactPage->location_image)
            <div class="col-lg-4 col-md-6">
                <div class="card border-0 shadow-sm">
                    <img src="{{ asset('storage/' . $contactPage->location_image) }}"
                         alt="หน้าร้าน" class="card-img-top" style="height: 250px; object-fit: cover;">
                    <div class="card-body text-center">
                        <h6 class="card-title">หน้าร้าน</h6>
                    </div>
                </div>
            </div>
            @endif

            @if($contactPage->interior_image)
            <div class="col-lg-4 col-md-6">
                <div class="card border-0 shadow-sm">
                    <img src="{{ asset('storage/' . $contactPage->interior_image) }}"
                         alt="ภายในร้าน" class="card-img-top" style="height: 250px; object-fit: cover;">
                    <div class="card-body text-center">
                        <h6 class="card-title">ภายในร้าน</h6>
                    </div>
                </div>
            </div>
            @endif

            @if($contactPage->parking_image)
            <div class="col-lg-4 col-md-6">
                <div class="card border-0 shadow-sm">
                    <img src="{{ asset('storage/' . $contactPage->parking_image) }}"
                         alt="ที่จอดรถ" class="card-img-top" style="height: 250px; object-fit: cover;">
                    <div class="card-body text-center">
                        <h6 class="card-title">ที่จอดรถ</h6>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</section>
@endif

<!-- Transportation & Parking Info -->
@if($contactPage->directions || $contactPage->parking_info || $contactPage->public_transport)
<section class="py-5">
    <div class="container">
        <h2 class="text-center text-primary mb-5">
            <i class="fas fa-route me-2"></i>การเดินทาง
        </h2>
        <div class="row g-4">
            @if($contactPage->directions)
            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-map-signs fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">คำแนะนำการเดินทาง</h5>
                        <p class="card-text">
                            {!! nl2br(e($contactPage->directions)) !!}
                        </p>
                    </div>
                </div>
            </div>
            @endif

            @if($contactPage->parking_info)
            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-parking fa-3x text-success mb-3"></i>
                        <h5 class="card-title">ที่จอดรถ</h5>
                        <p class="card-text">
                            {!! nl2br(e($contactPage->parking_info)) !!}
                        </p>
                    </div>
                </div>
            </div>
            @endif

            @if($contactPage->public_transport)
            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-bus fa-3x text-info mb-3"></i>
                        <h5 class="card-title">ขนส่งสาธารณะ</h5>
                        <p class="card-text">
                            {!! nl2br(e($contactPage->public_transport)) !!}
                        </p>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</section>
@endif

<!-- Map Section -->
@if($contactPage->map_embed)
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center text-primary mb-5">
            <i class="fas fa-map me-2"></i>แผนที่ร้าน
        </h2>
        <div class="row">
            <div class="col-12">
                <div class="map-container rounded shadow">
                    {!! $contactPage->map_embed !!}
                </div>
            </div>
        </div>
    </div>
</section>
@else
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center text-primary mb-4">แผนที่ร้าน</h2>
        <div class="row">
            <div class="col-12">
                <div class="bg-white rounded p-5 text-center shadow-sm">
                    <i class="fas fa-map fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">แผนที่ Google Maps</h5>
                    <p class="text-muted">สามารถเพิ่ม Google Maps embed code ในหลังบ้าน</p>
                </div>
            </div>
        </div>
    </div>
</section>
@endif

@push('styles')
<style>
.hero-section {
    position: relative;
    overflow: hidden;
}

.contact-item {
    transition: transform 0.3s ease;
}

.contact-item:hover {
    transform: translateX(5px);
}

.contact-icon {
    min-width: 50px;
}

.map-container iframe {
    width: 100%;
    height: 400px;
    border: 0;
    border-radius: 8px;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}
</style>
@endpush

@endsection
