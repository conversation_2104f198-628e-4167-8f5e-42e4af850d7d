@extends('layouts.app')

@section('title', 'รายละเอียดผู้ใช้ - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary fade-in-up">
                        <i class="fas fa-user me-2"></i>รายละเอียดผู้ใช้
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.users.index') }}">ผู้ใช้</a>
                            </li>
                            <li class="breadcrumb-item active">{{ $user->name }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>แก้ไข
                    </a>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับ
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-info-circle me-2"></i>ข้อมูลผู้ใช้
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">ชื่อ-นามสกุล</label>
                                <p class="form-control-plaintext">{{ $user->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">อีเมล</label>
                                <p class="form-control-plaintext">
                                    {{ $user->email }}
                                    @if($user->email_verified_at)
                                        <span class="badge bg-success ms-2">
                                            <i class="fas fa-check-circle"></i> ยืนยันแล้ว
                                        </span>
                                    @else
                                        <span class="badge bg-warning ms-2">
                                            <i class="fas fa-clock"></i> รอยืนยัน
                                        </span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">บทบาท</label>
                                <p class="form-control-plaintext">
                                    @if($user->role == 'admin')
                                        <span class="badge bg-danger fs-6">
                                            <i class="fas fa-crown"></i> ผู้ดูแลระบบ
                                        </span>
                                    @else
                                        <span class="badge bg-info fs-6">
                                            <i class="fas fa-user"></i> ผู้ใช้ทั่วไป
                                        </span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">วันที่สมัครสมาชิก</label>
                                <p class="form-control-plaintext">{{ $user->created_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                    </div>

                    @if($user->updated_at != $user->created_at)
                        <div class="mb-3">
                            <label class="form-label fw-bold">วันที่แก้ไขล่าสุด</label>
                            <p class="form-control-plaintext">{{ $user->updated_at->format('d/m/Y H:i') }}</p>
                        </div>
                    @endif

                    @if($user->email_verified_at)
                        <div class="mb-3">
                            <label class="form-label fw-bold">วันที่ยืนยันอีเมล</label>
                            <p class="form-control-plaintext">{{ $user->email_verified_at->format('d/m/Y H:i') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Profile Card -->
            <div class="card border-0 shadow-lg mb-4">
                <div class="card-header">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-user-circle me-2"></i>โปรไฟล์
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 100px; height: 100px;">
                        <i class="fas fa-user fa-3x text-white"></i>
                    </div>
                    <h5 class="mb-1">{{ $user->name }}</h5>
                    <p class="text-muted mb-3">{{ $user->email }}</p>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h5 text-primary mb-1">
                                    @if($user->role == 'admin')
                                        <i class="fas fa-crown text-warning"></i>
                                    @else
                                        <i class="fas fa-user text-info"></i>
                                    @endif
                                </div>
                                <small class="text-muted">{{ $user->role == 'admin' ? 'Admin' : 'User' }}</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h5 text-success mb-1">{{ $user->created_at->diffInDays() }}</div>
                            <small class="text-muted">วัน</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="card border-0 shadow-lg mb-4">
                <div class="card-header">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-cogs me-2"></i>การจัดการ
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>แก้ไขข้อมูล
                        </a>
                        
                        @if($user->role == 'user')
                            <form action="{{ route('admin.users.update', $user) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="role" value="admin">
                                <button type="submit" class="btn btn-success w-100" 
                                        onclick="return confirm('ต้องการเลื่อนผู้ใช้นี้เป็นผู้ดูแลระบบ?')">
                                    <i class="fas fa-user-shield me-2"></i>เลื่อนเป็น Admin
                                </button>
                            </form>
                        @elseif($user->id != auth()->id())
                            <form action="{{ route('admin.users.update', $user) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="role" value="user">
                                <button type="submit" class="btn btn-secondary w-100" 
                                        onclick="return confirm('ต้องการลดสิทธิ์ผู้ใช้นี้เป็นผู้ใช้ทั่วไป?')">
                                    <i class="fas fa-user-minus me-2"></i>ลดเป็น User
                                </button>
                            </form>
                        @endif

                        @if(!$user->email_verified_at)
                            <button type="button" class="btn btn-info w-100">
                                <i class="fas fa-envelope me-2"></i>ส่งอีเมลยืนยัน
                            </button>
                        @endif

                        <hr>

                        @if($user->id != auth()->id())
                            <form action="{{ route('admin.users.destroy', $user) }}" method="POST" 
                                  onsubmit="return confirm('ต้องการลบผู้ใช้นี้? การกระทำนี้ไม่สามารถยกเลิกได้')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-trash me-2"></i>ลบผู้ใช้
                                </button>
                            </form>
                        @else
                            <button type="button" class="btn btn-danger w-100" disabled>
                                <i class="fas fa-ban me-2"></i>ไม่สามารถลบตัวเองได้
                            </button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Stats Card -->
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-chart-bar me-2"></i>สถิติ
                    </h6>
                </div>
                <div class="card-body">
                    <div class="small text-muted">
                        <div class="d-flex justify-content-between mb-2">
                            <span>สมาชิกมาแล้ว:</span>
                            <span class="fw-bold">{{ $user->created_at->diffForHumans() }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>อัปเดตล่าสุด:</span>
                            <span class="fw-bold">{{ $user->updated_at->diffForHumans() }}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>สถานะอีเมล:</span>
                            <span class="fw-bold {{ $user->email_verified_at ? 'text-success' : 'text-warning' }}">
                                {{ $user->email_verified_at ? 'ยืนยันแล้ว' : 'รอยืนยัน' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
