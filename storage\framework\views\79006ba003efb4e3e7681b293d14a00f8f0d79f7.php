<?php $__env->startSection('title', $category->name . ' - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    <!-- Header Section -->
    <div class="text-center mb-5">
        <div class="d-inline-block position-relative">
            <h1 class="display-4 fw-bold text-primary mb-3"><?php echo e($category->name); ?></h1>
            <div class="position-absolute bottom-0 start-50 translate-middle-x" style="width: 100px; height: 4px; background: linear-gradient(90deg, var(--primary-color), var(--accent-color)); border-radius: 2px;"></div>
        </div>
        <?php if($category->description): ?>
            <p class="lead text-muted mt-4"><?php echo e($category->description); ?></p>
        <?php endif; ?>
    </div>

    <!-- Menu Items Grid -->
    <?php if($menuItems->count() > 0): ?>
        <?php if($category->slug === 'noodles'): ?>
            
            <?php
                $beefNoodles = $menuItems->filter(function($item) {
                    return $item->category->slug === 'noodle-beef';
                });
                $porkNoodles = $menuItems->filter(function($item) {
                    return $item->category->slug === 'noodle-pork';
                });
            ?>

            <?php if($beefNoodles->count() > 0): ?>
                <div class="mb-5">
                    <h3 class="text-danger mb-4">
                        <i class="fas fa-drumstick-bite me-2"></i>ก๋วยเตี๋ยวเนื้อ
                    </h3>
                    <div class="row g-4">
                        <?php $__currentLoopData = $beefNoodles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo $__env->make('menu.partials.menu-card', ['item' => $item], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if($porkNoodles->count() > 0): ?>
                <div class="mb-5">
                    <h3 class="text-danger mb-4">
                        <i class="fas fa-bacon me-2"></i>ก๋วยเตี๋ยวหมู
                    </h3>
                    <div class="row g-4">
                        <?php $__currentLoopData = $porkNoodles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo $__env->make('menu.partials.menu-card', ['item' => $item], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php else: ?>
            
            <div class="row g-4">
                <?php $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php echo $__env->make('menu.partials.menu-card', ['item' => $item], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>

        <!-- Back to Menu Button -->
        <div class="text-center mt-5">
            <a href="<?php echo e(route('menu.index')); ?>" class="btn btn-outline-primary btn-lg px-5 py-3">
                <i class="fas fa-arrow-left me-2"></i>กลับไปดูเมนูทั้งหมด
            </a>
        </div>
    <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-utensils fa-5x text-muted"></i>
            </div>
            <h3 class="text-muted mb-3">ยังไม่มีเมนูในหมวดหมู่นี้</h3>
            <p class="text-muted mb-4">กรุณาติดตามเมนูใหม่ๆ ที่จะมาเร็วๆ นี้</p>
            <a href="<?php echo e(route('menu.index')); ?>" class="btn btn-primary btn-lg px-5 py-3">
                <i class="fas fa-arrow-left me-2"></i>กลับไปดูเมนูทั้งหมด
            </a>
        </div>
    <?php endif; ?>
</div>

<style>
.menu-item-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.menu-item-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1) !important;
}

.menu-item-card:hover .card-img-top {
    transform: scale(1.05);
}

.price {
    position: relative;
}

.price::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--success), var(--accent-color));
    border-radius: 1px;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/menu/category.blade.php ENDPATH**/ ?>