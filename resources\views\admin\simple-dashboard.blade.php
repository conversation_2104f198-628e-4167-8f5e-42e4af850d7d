<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - ร้านก๋วยเตี๋ยวเรือเข้าท่า</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
            min-height: 100vh;
        }
        .dashboard-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="dashboard-card">
            <div class="card-header bg-primary text-white">
                <h1 class="h3 mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    หน้าจัดการระบบ - ร้านก๋วยเตี๋ยวเรือเข้าท่า
                </h1>
                <p class="mb-0">ยินดีต้อนรับ {{ Auth::user()->name ?? 'Admin' }}</p>
            </div>
            
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <i class="fas fa-list fa-3x mb-3"></i>
                            <h3>{{ $stats['categories'] ?? 0 }}</h3>
                            <p>หมวดหมู่อาหาร</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card text-center" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                            <i class="fas fa-utensils fa-3x mb-3"></i>
                            <h3>{{ $stats['menu_items'] ?? 0 }}</h3>
                            <p>เมนูทั้งหมด</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card text-center" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);">
                            <i class="fas fa-newspaper fa-3x mb-3"></i>
                            <h3>{{ $stats['news'] ?? 0 }}</h3>
                            <p>ข่าวสาร</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card text-center" style="background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <h3>{{ Auth::user()->role ?? 'admin' }}</h3>
                            <p>สถานะผู้ใช้</p>
                        </div>
                    </div>
                </div>
                
                <hr class="my-4">
                
                <div class="row">
                    <div class="col-md-12">
                        <h4><i class="fas fa-cogs me-2"></i>เมนูจัดการ</h4>
                        <div class="row mt-3">
                            <div class="col-md-4 mb-3">
                                <a href="{{ route('admin.categories.index') }}" class="btn btn-primary w-100 py-3">
                                    <i class="fas fa-list fa-2x d-block mb-2"></i>
                                    จัดการหมวดหมู่
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="{{ route('admin.menu-items.index') }}" class="btn btn-success w-100 py-3">
                                    <i class="fas fa-utensils fa-2x d-block mb-2"></i>
                                    จัดการเมนูอาหาร
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="{{ route('admin.news.index') }}" class="btn btn-info w-100 py-3">
                                    <i class="fas fa-newspaper fa-2x d-block mb-2"></i>
                                    จัดการข่าวสาร
                                </a>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-4 mb-3">
                                <a href="{{ route('admin.users.index') }}" class="btn btn-warning w-100 py-3">
                                    <i class="fas fa-users fa-2x d-block mb-2"></i>
                                    จัดการผู้ใช้
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="{{ route('admin.hero-sliders.index') }}" class="btn btn-purple w-100 py-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; color: white;">
                                    <i class="fas fa-images fa-2x d-block mb-2"></i>
                                    จัดการสไลด์
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="{{ route('admin.restaurant-info.index') }}" class="btn btn-secondary w-100 py-3">
                                    <i class="fas fa-store fa-2x d-block mb-2"></i>
                                    ข้อมูลร้าน
                                </a>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-4 mb-3">
                                <a href="{{ route('logout') }}" class="btn btn-danger w-100 py-3"
                                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                    <i class="fas fa-sign-out-alt fa-2x d-block mb-2"></i>
                                    ออกจากระบบ
                                </a>
                                <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                    @csrf
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
