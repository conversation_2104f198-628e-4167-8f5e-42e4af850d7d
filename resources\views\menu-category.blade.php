@extends('layouts.app')

@section('title', $category->name . ' - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<!-- <PERSON> Header -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ route('home') }}" class="text-white-50">หน้าหลัก</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('menu.index') }}" class="text-white-50">เมนูอาหาร</a>
                        </li>
                        <li class="breadcrumb-item active text-white" aria-current="page">
                            {{ $category->name }}
                        </li>
                    </ol>
                </nav>
                
                <div class="d-flex align-items-center">
                    @if($category->image)
                        <img src="{{ asset('storage/' . $category->image) }}" 
                             alt="{{ $category->name }}" 
                             class="rounded-circle me-4" 
                             style="width: 80px; height: 80px; object-fit: cover;">
                    @else
                        <div class="bg-white bg-opacity-25 rounded-circle me-4 d-flex align-items-center justify-content-center" 
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-utensils fa-2x text-white"></i>
                        </div>
                    @endif
                    
                    <div>
                        <h1 class="display-5 fw-bold mb-2">{{ $category->name }}</h1>
                        <p class="lead mb-0">{{ $category->description }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Category Navigation -->
<section class="py-3 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex flex-wrap gap-2">
                    <a href="{{ route('menu.index') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-th-large me-1"></i>ทั้งหมด
                    </a>
                    @foreach($categories as $cat)
                        <a href="{{ route('menu.category', $cat->slug) }}" 
                           class="btn btn-sm {{ $cat->id == $category->id ? 'btn-primary' : 'btn-outline-primary' }}">
                            {{ $cat->name }}
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Menu Items -->
<section class="py-5">
    <div class="container">
        @if($menuItems->count() > 0)
            <div class="row">
                @foreach($menuItems as $item)
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 border-0 shadow-sm">
                            @if($item->image)
                                <img src="{{ asset('storage/' . $item->image) }}" 
                                     class="card-img-top" 
                                     alt="{{ $item->name }}" 
                                     style="height: 250px; object-fit: cover;">
                            @else
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                     style="height: 250px;">
                                    <i class="fas fa-image fa-4x text-muted"></i>
                                </div>
                            @endif
                            
                            <div class="card-body d-flex flex-column">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="card-title text-primary mb-0">{{ $item->name }}</h5>
                                    @if($item->is_featured)
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-star me-1"></i>แนะนำ
                                        </span>
                                    @endif
                                </div>
                                
                                @if($item->description)
                                    <p class="card-text text-muted flex-grow-1">{{ $item->description }}</p>
                                @endif
                                
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <strong class="text-primary h4 mb-0">{{ $item->formatted_price }}</strong>
                                        @if(!$item->is_active)
                                            <span class="badge bg-danger">ไม่พร้อมจำหน่าย</span>
                                        @else
                                            <span class="badge bg-success">พร้อมจำหน่าย</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-utensils fa-4x text-muted mb-4"></i>
                <h3 class="text-muted">ยังไม่มีเมนูในหมวดหมู่นี้</h3>
                <p class="text-muted mb-4">กรุณาเลือกหมวดหมู่อื่น หรือกลับไปดูเมนูทั้งหมด</p>
                <a href="{{ route('menu.index') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>กลับไปดูเมนูทั้งหมด
                </a>
            </div>
        @endif
    </div>
</section>

<!-- Related Categories -->
@if($categories->count() > 1)
<section class="py-5 bg-light">
    <div class="container">
        <h3 class="text-center text-primary mb-4">หมวดหมู่อื่นๆ</h3>
        <div class="row">
            @foreach($categories->where('id', '!=', $category->id)->take(4) as $relatedCategory)
                <div class="col-lg-3 col-md-6 mb-3">
                    <a href="{{ route('menu.category', $relatedCategory->slug) }}" 
                       class="text-decoration-none">
                        <div class="card h-100 border-0 shadow-sm">
                            @if($relatedCategory->image)
                                <img src="{{ asset('storage/' . $relatedCategory->image) }}" 
                                     class="card-img-top" 
                                     alt="{{ $relatedCategory->name }}" 
                                     style="height: 120px; object-fit: cover;">
                            @else
                                <div class="card-img-top bg-primary d-flex align-items-center justify-content-center" 
                                     style="height: 120px;">
                                    <i class="fas fa-utensils fa-2x text-white"></i>
                                </div>
                            @endif
                            
                            <div class="card-body text-center">
                                <h6 class="card-title text-primary mb-1">{{ $relatedCategory->name }}</h6>
                                <small class="text-muted">{{ $relatedCategory->menuItems->count() }} เมนู</small>
                            </div>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif
@endsection
