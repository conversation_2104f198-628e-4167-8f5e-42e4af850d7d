@extends('layouts.admin')

@section('title', 'แก้ไขข้อความหน้าแรก - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ route('admin.hero-content.index') }}" class="btn btn-outline-secondary btn-back">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับสู่จัดการข้อความหน้าแรก
            </a>
        </div>
    </div>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 text-primary">
                    <i class="fas fa-edit me-2"></i>แก้ไขข้อความหน้าแรก
                </h1>
            </div>
        </div>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ route('admin.hero-content.update') }}" method="POST">
        @csrf
        @method('PUT')

        <div class="row g-4">
            <!-- Hero Content Form -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-home me-2"></i>ข้อความ Hero Section
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <label for="name" class="form-label">
                                <i class="fas fa-heading me-2"></i>ชื่อร้าน (หัวข้อหลัก) <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $restaurantInfo->name) }}" required
                                   placeholder="เช่น ร้านก๋วยเตี๋ยวเรือเข้าท่า">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">ข้อความนี้จะแสดงเป็นหัวข้อหลักขนาดใหญ่ในหน้าแรก</div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left me-2"></i>คำอธิบายร้าน (หัวข้อรอง)
                            </label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4"
                                      placeholder="เช่น ก๋วยเตี๋ยวเรือต้นตำรับ รสชาติดั้งเดิม ด้วยสูตรลับเฉพาะตัว เครื่องเทศครบครอง">{{ old('description', $restaurantInfo->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">ข้อความนี้จะแสดงเป็นหัวข้อรองใต้ชื่อร้าน</div>
                        </div>

                        <div class="mb-4">
                            <label for="tagline" class="form-label">
                                <i class="fas fa-quote-right me-2"></i>สโลแกน/ข้อความโปรโมท
                            </label>
                            <textarea class="form-control @error('tagline') is-invalid @enderror" 
                                      id="tagline" name="tagline" rows="3"
                                      placeholder="เช่น ลิ้มลองรสชาติความอร่อยระดับตำนาน ที่ยังคงไว้ซึ่งเอกลักษณ์ไทย">{{ old('tagline', $restaurantInfo->tagline) }}</textarea>
                            @error('tagline')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">ข้อความนี้จะแสดงเป็นสโลแกนหรือข้อความโปรโมทใต้คำอธิบายร้าน</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-eye me-2"></i>ตัวอย่าง
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="hero-preview p-3 text-center text-white" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('{{ $restaurantInfo->background_image ? asset('storage/' . $restaurantInfo->background_image) : asset('images/restaurant/background.jpg') }}'); background-size: cover; background-position: center; min-height: 250px; display: flex; flex-direction: column; justify-content: center;">
                            <h3 class="fw-bold mb-2" id="preview-name">
                                {{ $restaurantInfo->name ?: 'ร้านก๋วยเตี๋ยวเรือเข้าท่า' }}
                            </h3>
                            <p class="mb-2 small" id="preview-description">
                                {{ $restaurantInfo->description ?: 'ก๋วยเตี๋ยวเรือต้นตำรับ รสชาติดั้งเดิม ด้วยสูตรลับเฉพาะตัว เครื่องเทศครบครอง' }}
                            </p>
                            <p class="mb-0 small" id="preview-tagline">
                                {{ $restaurantInfo->tagline ?: 'ลิ้มลองรสชาติความอร่อยระดับตำนาน ที่ยังคงไว้ซึ่งเอกลักษณ์ไทย' }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Tips -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>เคล็ดลับ
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0 small">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                ใช้ชื่อร้านที่จดจำง่าย
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                คำอธิบายควรสั้นกระชับ
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                สโลแกนควรดึงดูดใจลูกค้า
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-check text-success me-2"></i>
                                ทดสอบดูในหน้าเว็บไซต์จริง
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-end gap-2">
                    <a href="{{ route('admin.hero-content.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>ยกเลิก
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>บันทึกข้อความ
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

@push('styles')
<style>
.card {
    transition: all 0.3s ease;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
    border-bottom: none !important;
}

.hero-preview {
    border-radius: 0.375rem;
}

.btn-back {
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    const taglineInput = document.getElementById('tagline');
    
    const previewName = document.getElementById('preview-name');
    const previewDescription = document.getElementById('preview-description');
    const previewTagline = document.getElementById('preview-tagline');
    
    nameInput.addEventListener('input', function() {
        previewName.textContent = this.value || 'ร้านก๋วยเตี๋ยวเรือเข้าท่า';
    });
    
    descriptionInput.addEventListener('input', function() {
        previewDescription.textContent = this.value || 'ก๋วยเตี๋ยวเรือต้นตำรับ รสชาติดั้งเดิม ด้วยสูตรลับเฉพาะตัว เครื่องเทศครบครอง';
    });
    
    taglineInput.addEventListener('input', function() {
        previewTagline.textContent = this.value || 'ลิ้มลองรสชาติความอร่อยระดับตำนาน ที่ยังคงไว้ซึ่งเอกลักษณ์ไทย';
    });
});
</script>
@endpush
@endsection
