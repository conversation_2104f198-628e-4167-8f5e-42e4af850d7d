@extends('layouts.app')

@section('title', 'รายละเอียดข่าวสาร - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary fade-in-up">
                        <i class="fas fa-newspaper me-2"></i>รายละเอียดข่าวสาร
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.news.index') }}">ข่าวสาร</a>
                            </li>
                            <li class="breadcrumb-item active">{{ Str::limit($news->title, 30) }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.news.edit', $news) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>แก้ไข
                    </a>
                    <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับ
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-info-circle me-2"></i>ข้อมูลข่าวสาร
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="mb-4">
                        <h2 class="h4 text-primary mb-3">{{ $news->title }}</h2>
                        
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            @if($news->is_featured)
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star"></i> ข่าวเด่น
                                </span>
                            @endif
                            @if($news->is_published)
                                <span class="badge bg-success">
                                    <i class="fas fa-eye"></i> เผยแพร่แล้ว
                                </span>
                            @else
                                <span class="badge bg-secondary">
                                    <i class="fas fa-eye-slash"></i> ร่าง
                                </span>
                            @endif
                            <span class="badge bg-info">
                                <i class="fas fa-calendar"></i> {{ $news->created_at->format('d/m/Y H:i') }}
                            </span>
                        </div>

                        @if($news->excerpt)
                            <div class="alert alert-light border-start border-primary border-4 mb-4">
                                <h6 class="fw-bold mb-2">สรุปข่าว</h6>
                                <p class="mb-0">{{ $news->excerpt }}</p>
                            </div>
                        @endif

                        @if($news->image)
                            <div class="text-center mb-4">
                                <img src="{{ asset('storage/' . $news->image) }}" alt="{{ $news->title }}" 
                                     class="img-fluid rounded shadow-sm" style="max-height: 400px;">
                            </div>
                        @endif

                        <div class="content">
                            <h6 class="fw-bold mb-3">เนื้อหาข่าว</h6>
                            <div class="text-muted lh-lg">
                                {!! nl2br(e($news->content)) !!}
                            </div>
                        </div>
                    </div>

                    @if($news->updated_at != $news->created_at)
                        <hr>
                        <div class="text-muted small">
                            <i class="fas fa-edit me-1"></i>
                            แก้ไขล่าสุด: {{ $news->updated_at->format('d/m/Y H:i') }}
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Actions Card -->
            <div class="card border-0 shadow-lg mb-4">
                <div class="card-header">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-cogs me-2"></i>การจัดการ
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.news.edit', $news) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>แก้ไขข้อมูล
                        </a>
                        
                        @if($news->is_published)
                            <form action="{{ route('admin.news.update', $news) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="is_published" value="0">
                                <button type="submit" class="btn btn-secondary w-100" 
                                        onclick="return confirm('ต้องการยกเลิกการเผยแพร่ข่าวนี้?')">
                                    <i class="fas fa-eye-slash me-2"></i>ยกเลิกการเผยแพร่
                                </button>
                            </form>
                        @else
                            <form action="{{ route('admin.news.update', $news) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="is_published" value="1">
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-eye me-2"></i>เผยแพร่ข่าว
                                </button>
                            </form>
                        @endif

                        @if($news->is_featured)
                            <form action="{{ route('admin.news.update', $news) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="is_featured" value="0">
                                <button type="submit" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-star-half-alt me-2"></i>ยกเลิกข่าวเด่น
                                </button>
                            </form>
                        @else
                            <form action="{{ route('admin.news.update', $news) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="is_featured" value="1">
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-star me-2"></i>ตั้งเป็นข่าวเด่น
                                </button>
                            </form>
                        @endif

                        <hr>

                        <form action="{{ route('admin.news.destroy', $news) }}" method="POST" 
                              onsubmit="return confirm('ต้องการลบข่าวนี้? การกระทำนี้ไม่สามารถยกเลิกได้')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash me-2"></i>ลบข่าว
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Info Card -->
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-info-circle me-2"></i>ข้อมูลเพิ่มเติม
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h5 text-primary mb-1">{{ $news->created_at->format('d') }}</div>
                                <small class="text-muted">{{ $news->created_at->format('M Y') }}</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h5 text-success mb-1">{{ str_word_count(strip_tags($news->content)) }}</div>
                            <small class="text-muted">คำ</small>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="small text-muted">
                        <div class="d-flex justify-content-between mb-2">
                            <span>สร้างเมื่อ:</span>
                            <span>{{ $news->created_at->format('d/m/Y H:i') }}</span>
                        </div>
                        @if($news->updated_at != $news->created_at)
                            <div class="d-flex justify-content-between">
                                <span>แก้ไขล่าสุด:</span>
                                <span>{{ $news->updated_at->format('d/m/Y H:i') }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
