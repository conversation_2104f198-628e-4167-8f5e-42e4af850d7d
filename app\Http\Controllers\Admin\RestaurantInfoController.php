<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\RestaurantInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class RestaurantInfoController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $restaurantInfo = RestaurantInfo::getInfo();
        return view('admin.restaurant-info.index', compact('restaurantInfo'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit()
    {
        $restaurantInfo = RestaurantInfo::getInfo();
        return view('admin.restaurant-info.edit', compact('restaurantInfo'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'facebook' => 'nullable|string|max:255',
            'line' => 'nullable|string|max:255',
            'instagram' => 'nullable|string|max:255',
            'open_time' => 'nullable|date_format:H:i',
            'close_time' => 'nullable|date_format:H:i',
            'open_days' => 'nullable|array',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'background_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'map_embed' => 'nullable|string',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);

        $restaurantInfo = RestaurantInfo::getInfo();
        $data = $request->all();

        // Handle logo upload
        if ($request->hasFile('logo')) {
            if ($restaurantInfo->logo) {
                Storage::disk('public')->delete($restaurantInfo->logo);
            }
            $data['logo'] = $request->file('logo')->store('restaurant', 'public');
        }

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            if ($restaurantInfo->cover_image) {
                Storage::disk('public')->delete($restaurantInfo->cover_image);
            }
            $data['cover_image'] = $request->file('cover_image')->store('restaurant', 'public');
        }

        // Handle background image upload
        if ($request->hasFile('background_image')) {
            if ($restaurantInfo->background_image) {
                Storage::disk('public')->delete($restaurantInfo->background_image);
            }
            $data['background_image'] = $request->file('background_image')->store('restaurant', 'public');
        }

        // If no existing record, create new one
        if (!$restaurantInfo->exists) {
            RestaurantInfo::create($data);
        } else {
            $restaurantInfo->update($data);
        }

        return redirect()->route('admin.restaurant-info.index')
            ->with('success', 'ข้อมูลร้านถูกอัปเดตเรียบร้อยแล้ว');
    }
}
