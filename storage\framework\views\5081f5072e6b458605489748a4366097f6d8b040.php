<?php $__env->startSection('title', 'จัดการสไลด์หน้าแรก'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-images me-2"></i>จัดการสไลด์หน้าแรก
                        </h4>
                        <a href="<?php echo e(route('admin.hero-sliders.create')); ?>" class="btn btn-light">
                            <i class="fas fa-plus me-1"></i>เพิ่มสไลด์ใหม่
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if($sliders->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="80">รูปภาพ</th>
                                        <th>หัวข้อ</th>
                                        <th>หัวข้อรอง</th>
                                        <th width="100">ลำดับ</th>
                                        <th width="100">สถานะ</th>
                                        <th width="150">จัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <?php if($slider->image): ?>
                                                    <img src="<?php echo e(asset('storage/' . $slider->image)); ?>" 
                                                         alt="<?php echo e($slider->title); ?>" 
                                                         class="img-thumbnail" 
                                                         style="width: 60px; height: 40px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light d-flex align-items-center justify-content-center" 
                                                         style="width: 60px; height: 40px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo e($slider->title); ?></strong>
                                            </td>
                                            <td><?php echo e($slider->subtitle ?? '-'); ?></td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo e($slider->sort_order ?? 0); ?></span>
                                            </td>
                                            <td>
                                                <?php if($slider->is_active): ?>
                                                    <span class="badge bg-success">เปิดใช้งาน</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">ปิดใช้งาน</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.hero-sliders.edit', $slider)); ?>" 
                                                       class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('admin.hero-sliders.destroy', $slider)); ?>" 
                                                          method="POST" 
                                                          class="d-inline"
                                                          onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบสไลด์นี้?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">ยังไม่มีสไลด์</h5>
                            <p class="text-muted">เริ่มต้นสร้างสไลด์แรกของคุณ</p>
                            <a href="<?php echo e(route('admin.hero-sliders.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>เพิ่มสไลด์ใหม่
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/admin/hero-sliders/index.blade.php ENDPATH**/ ?>