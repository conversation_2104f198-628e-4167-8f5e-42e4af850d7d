<div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 border-0 shadow-lg menu-item-card">
        <?php if($item->image): ?>
            <div class="position-relative overflow-hidden" style="height: 250px;">
                <img src="<?php echo e(asset('storage/' . $item->image)); ?>" 
                     class="card-img-top h-100 w-100" 
                     style="object-fit: cover; transition: transform 0.3s ease;"
                     alt="<?php echo e($item->name); ?>">
                <div class="position-absolute top-0 end-0 m-3">
                    <?php if($item->is_featured): ?>
                        <span class="badge bg-warning text-dark px-3 py-2">
                            <i class="fas fa-star me-1"></i>แนะนำ
                        </span>
                    <?php endif; ?>
                </div>
                <?php if(isset($item->category)): ?>
                    <div class="position-absolute top-0 start-0 m-3">
                        <span class="badge bg-primary px-3 py-2">
                            <?php echo e($item->category->name); ?>

                        </span>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 250px;">
                <i class="fas fa-utensils fa-3x text-muted"></i>
            </div>
        <?php endif; ?>

        <div class="card-body d-flex flex-column">
            <h5 class="card-title fw-bold text-primary mb-2"><?php echo e($item->name); ?></h5>
            
            <?php if($item->description): ?>
                <p class="card-text text-muted flex-grow-1"><?php echo e($item->description); ?></p>
            <?php endif; ?>

            <div class="d-flex justify-content-between align-items-center mt-auto">
                <div class="price">
                    <span class="h4 fw-bold text-success mb-0"><?php echo e(number_format($item->price)); ?> ฿</span>
                </div>
                <a href="<?php echo e(route('menu.show', $item->id)); ?>" class="btn btn-primary btn-sm px-3">
                    <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                </a>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/menu/partials/menu-card.blade.php ENDPATH**/ ?>