<?php $__env->startSection('title', 'ข่าวสารประชาสัมพันธ์ - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section position-relative overflow-hidden">
    <div class="container position-relative">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <div class="fade-in-up">
                    <h1 class="display-4 fw-bold mb-4 text-white text-shadow">
                        <i class="fas fa-newspaper me-3 text-warning"></i>
                        ข่าวสารประชาสัมพันธ์
                    </h1>
                    <p class="lead mb-5 text-white-75 fs-5">
                        ติดตามข่าวสารและกิจกรรมใหม่ๆ จากร้านก๋วยเตี๋ยวเรือเข้าท่า
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured News Section -->
<?php if($featuredNews->count() > 0): ?>
<section class="py-5 bg-white">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold">
                    <i class="fas fa-star text-warning me-3"></i>ข่าวสารแนะนำ
                </h2>
                <p class="lead text-muted">ข่าวสารสำคัญที่คุณไม่ควรพลาด</p>
            </div>
        </div>
        
        <div class="row g-4">
            <?php $__currentLoopData = $featuredNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-md-6">
                    <article class="card h-100 border-0 shadow-lg news-card" style="animation-delay: <?php echo e($index * 0.1); ?>s;">
                        <?php if($item->image): ?>
                            <div class="position-relative overflow-hidden">
                                <img src="<?php echo e(asset('storage/' . $item->image)); ?>" 
                                     class="card-img-top news-image" 
                                     alt="<?php echo e($item->title); ?>" 
                                     style="height: 250px; object-fit: cover;">
                                <div class="position-absolute top-0 end-0 p-3">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star me-1"></i>แนะนำ
                                    </span>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="card-img-top bg-gradient d-flex align-items-center justify-content-center" 
                                 style="height: 250px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                                <i class="fas fa-newspaper fa-4x text-white opacity-50"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="card-body d-flex flex-column">
                            <div class="mb-3">
                                <h5 class="card-title text-primary fw-bold mb-2"><?php echo e($item->title); ?></h5>
                                <p class="card-text text-muted"><?php echo e($item->short_content); ?></p>
                            </div>
                            
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo e($item->formatted_published_at); ?>

                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        <?php echo e($item->creator->name); ?>

                                    </small>
                                </div>
                                <a href="<?php echo e(route('news.show', $item)); ?>" class="btn btn-primary w-100">
                                    <i class="fas fa-arrow-right me-2"></i>อ่านต่อ
                                </a>
                            </div>
                        </div>
                    </article>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- All News Section -->
<section class="py-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold">
                    <i class="fas fa-list me-3 text-primary"></i>ข่าวสารทั้งหมด
                </h2>
            </div>
        </div>
        
        <?php if($news->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-6 col-md-6">
                        <article class="card h-100 border-0 shadow news-card-small" style="animation-delay: <?php echo e($index * 0.05); ?>s;">
                            <div class="row g-0 h-100">
                                <div class="col-md-4">
                                    <?php if($item->image): ?>
                                        <img src="<?php echo e(asset('storage/' . $item->image)); ?>" 
                                             class="img-fluid rounded-start h-100" 
                                             alt="<?php echo e($item->title); ?>" 
                                             style="object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light h-100 d-flex align-items-center justify-content-center rounded-start">
                                            <i class="fas fa-newspaper fa-2x text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-8">
                                    <div class="card-body d-flex flex-column h-100">
                                        <div>
                                            <h6 class="card-title text-primary fw-bold mb-2"><?php echo e($item->title); ?></h6>
                                            <p class="card-text text-muted small"><?php echo e(Str::limit($item->short_content, 100)); ?></p>
                                        </div>
                                        
                                        <div class="mt-auto">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    <?php echo e($item->published_at->format('d/m/Y')); ?>

                                                </small>
                                                <?php if($item->is_featured): ?>
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="fas fa-star me-1"></i>แนะนำ
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <a href="<?php echo e(route('news.show', $item)); ?>" class="btn btn-outline-primary btn-sm">
                                                อ่านต่อ <i class="fas fa-arrow-right ms-1"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            
            <!-- Pagination -->
            <?php if($news->hasPages()): ?>
                <div class="row mt-5">
                    <div class="col-12 d-flex justify-content-center">
                        <?php echo e($news->links()); ?>

                    </div>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-5x text-muted opacity-50 mb-4"></i>
                <h4 class="text-muted mb-3">ยังไม่มีข่าวสาร</h4>
                <p class="text-muted">กรุณาติดตามข่าวสารใหม่ๆ ในอนาคต</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<style>
.news-card {
    transition: all 0.4s ease;
    transform: translateY(0);
}

.news-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.news-card:hover .news-image {
    transform: scale(1.05);
}

.news-image {
    transition: transform 0.4s ease;
}

.news-card-small {
    transition: all 0.3s ease;
}

.news-card-small:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.text-white-75 {
    color: rgba(255,255,255,0.9) !important;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/news/index.blade.php ENDPATH**/ ?>