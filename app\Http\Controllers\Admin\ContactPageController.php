<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactPage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ContactPageController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $contactPage = ContactPage::getInfo();
        return view('admin.contact-page.index', compact('contactPage'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit()
    {
        $contactPage = ContactPage::getInfo();
        return view('admin.contact-page.edit', compact('contactPage'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'hero_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'line_id' => 'nullable|string|max:100',
            'facebook' => 'nullable|url|max:255',
            'instagram' => 'nullable|url|max:255',
            'open_time' => 'nullable|date_format:H:i',
            'close_time' => 'nullable|date_format:H:i',
            'open_days' => 'nullable|array',
            'special_hours' => 'nullable|string',
            'map_embed' => 'nullable|string',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'directions' => 'nullable|string',
            'location_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'interior_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'parking_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'parking_info' => 'nullable|string',
            'public_transport' => 'nullable|string',
            'additional_info' => 'nullable|string',
        ]);

        $contactPage = ContactPage::getInfo();
        $data = $request->all();

        // Handle image uploads
        $imageFields = ['hero_image', 'location_image', 'interior_image', 'parking_image'];

        foreach ($imageFields as $field) {
            if ($request->hasFile($field)) {
                // Delete old image if exists
                if ($contactPage->$field) {
                    Storage::disk('public')->delete($contactPage->$field);
                }
                // Store new image
                $data[$field] = $request->file($field)->store('contact-page', 'public');
            }
        }

        // If no existing record, create new one
        if (!$contactPage->exists) {
            ContactPage::create($data);
        } else {
            $contactPage->update($data);
        }

        return redirect()->route('admin.contact-page.index')
                        ->with('success', 'อัปเดตข้อมูลหน้าติดต่อเรียบร้อยแล้ว');
    }
}
