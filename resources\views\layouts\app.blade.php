<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'ร้านก๋วยเตี๋ยวเรือเข้าท่า')</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2691E;
            --accent-color: #CD853F;
            --cream-color: #F5F5DC;
            --dark-red: #8B0000;
            --text-dark: #2C1810;
            --gold-color: #DAA520;
            --light-brown: #DEB887;
            --boat-blue: #4682B4;
            --water-blue: #87CEEB;
            --wood-brown: #A0522D;
            --warm-orange: #FF8C00;
        }

        body {
            font-family: 'Sarabun', sans-serif;
            background: linear-gradient(135deg, var(--cream-color) 0%, #FFF8DC 100%);
            color: var(--text-dark);
            min-height: 100vh;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--wood-brown), var(--primary-color), var(--boat-blue));
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
            border-bottom: 3px solid var(--gold-color);
            position: relative;
        }

        .navbar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><path d="M0,10 Q25,0 50,10 T100,10 L100,20 L0,20 Z" fill="%23ffffff" opacity="0.1"/></svg>');
            background-size: 200px 20px;
            background-repeat: repeat-x;
            background-position: bottom;
            pointer-events: none;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-brand img {
            height: 40px;
            width: auto;
            margin-right: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: var(--cream-color) !important;
            transform: translateY(-2px);
        }
        
        .dropdown-menu {
            background-color: var(--cream-color);
            border: 2px solid var(--accent-color);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            padding: 0.75rem 0;
            min-width: 320px;
            max-width: 400px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .dropdown-item {
            color: var(--text-dark);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border-radius: 0;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
            color: white;
            transform: translateX(5px);
        }

        .dropdown-item.ps-4:hover {
            padding-left: 2.5rem;
        }

        .dropdown-item.ps-5:hover {
            padding-left: 3rem;
        }

        .dropdown-header {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0.5rem 1.5rem;
        }

        .dropdown-divider {
            border-color: var(--accent-color);
            opacity: 0.3;
            margin: 0.5rem 0;
        }

        /* Custom scrollbar for dropdown */
        .dropdown-menu::-webkit-scrollbar {
            width: 6px;
        }

        .dropdown-menu::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
            border-radius: 3px;
        }

        .dropdown-menu::-webkit-scrollbar-thumb {
            background: var(--accent-color);
            border-radius: 3px;
        }

        .dropdown-menu::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .footer {
            background: linear-gradient(135deg, var(--text-dark), var(--primary-color));
            color: white;
            padding: 40px 0 20px;
            margin-top: 50px;
        }
        
        .hero-section {
            background: linear-gradient(rgba(139, 69, 19, 0.7), rgba(210, 105, 30, 0.7)), 
                        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 300"><path fill="%23F5F5DC" d="M0,100 C150,200 350,0 500,100 C650,200 850,0 1000,100 L1000,300 L0,300 Z"/></svg>');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border: 1px solid rgba(139, 69, 19, 0.1);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            border-color: var(--gold-color);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-bottom: none;
            padding: 1.25rem 1.5rem;
        }
        
        .section-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 30px;
            position: relative;
            text-align: center;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-color), var(--gold-color));
            border-radius: 2px;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .btn {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: none;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .badge {
            border-radius: 20px;
            padding: 0.5em 1em;
            font-weight: 500;
        }

        .table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .table th {
            background: linear-gradient(135deg, var(--light-brown), var(--cream-color));
            color: var(--text-dark);
            font-weight: 600;
            border: none;
            padding: 1rem;
        }

        .table td {
            padding: 1rem;
            vertical-align: middle;
            border-color: rgba(139, 69, 19, 0.1);
        }

        .table tbody tr:hover {
            background-color: rgba(218, 165, 32, 0.1);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .alert {
            border-radius: 15px;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid rgba(139, 69, 19, 0.2);
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--gold-color);
            box-shadow: 0 0 0 0.2rem rgba(218, 165, 32, 0.25);
        }

        .breadcrumb {
            background: rgba(255,255,255,0.8);
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            backdrop-filter: blur(10px);
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: var(--primary-color);
            font-weight: bold;
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <img src="{{ asset('images/logo/logo.jpg') }}" alt="ก๋วยเตี๋ยวเรือเข้าท่า" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';" style="height: 40px; width: auto;">
                <i class="fas fa-ship me-2" style="display: none;"></i>
                ก๋วยเตี๋ยวเรือเข้าท่า
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('home') }}">หน้าหลัก</a>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="menuDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-utensils me-1"></i>เมนูอาหาร
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="menuDropdown">
                            {{-- เมนูแนะนำ --}}
                            <li><a class="dropdown-item" href="{{ route('menu.category', 'recommended') }}">
                                <i class="fas fa-star me-2 text-warning"></i>เมนูแนะนำ
                            </a></li>

                            {{-- ของทานเล่น --}}
                            <li><a class="dropdown-item" href="{{ route('menu.category', 'appetizers') }}">
                                <i class="fas fa-cookie-bite me-2 text-info"></i>ของทานเล่น
                            </a></li>

                            {{-- เครื่องดื่ม --}}
                            <li><a class="dropdown-item" href="{{ route('menu.category', 'beverages') }}">
                                <i class="fas fa-coffee me-2 text-success"></i>เครื่องดื่ม
                            </a></li>

                            {{-- ก๋วยเตี๋ยว --}}
                            <li><a class="dropdown-item" href="{{ route('menu.category', 'noodles') }}">
                                <i class="fas fa-bowl-food me-2 text-danger"></i>ก๋วยเตี๋ยว
                            </a></li>

                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center fw-bold text-primary" href="{{ route('menu.index') }}">
                                <i class="fas fa-list me-2"></i>ดูเมนูทั้งหมด
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('news.index') }}">
                            <i class="fas fa-newspaper me-1"></i>ข่าวสาร
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('about') }}">
                            <i class="fas fa-info-circle me-1"></i>เกี่ยวกับ
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    @auth
                        @if(Auth::user()->isAdmin())
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('admin.dashboard') }}">
                                    <i class="fas fa-cog me-1"></i>จัดการระบบ
                                </a>
                            </li>
                        @endif
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i>{{ Auth::user()->name }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li>
                                    <form action="{{ route('logout') }}" method="POST" class="d-inline">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-1"></i>ออกจากระบบ
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    @else
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('login') }}">
                                <i class="fas fa-sign-in-alt me-1"></i>เข้าสู่ระบบ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('register') }}">
                                <i class="fas fa-user-plus me-1"></i>สมัครสมาชิก
                            </a>
                        </li>
                    @endauth
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main style="margin-top: 76px;">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-ship me-2"></i>ร้านก๋วยเตี๋ยวเรือเข้าท่า</h5>
                    <p>ก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม ด้วยน้ำซุปเข้มข้น<br>
                    เครื่องเทศครบเครื่อง อร่อยถูกปาก</p>
                </div>
                <div class="col-md-6">
                    <h5>ติดต่อเรา</h5>
                    <p><i class="fas fa-map-marker-alt me-2"></i>123 ถนนริมน้า เขตพระนคร กรุงเทพฯ 10200</p>
                    <p><i class="fas fa-phone me-2"></i>02-123-4567</p>
                    <p><i class="fas fa-clock me-2"></i>เปิดทุกวัน 08:00 - 20:00 น.</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 ร้านก๋วยเตี๋ยวเรือเข้าท่า. สงวนลิขสิทธิ์.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- CSRF Token Setup -->
    <script>
        // Setup CSRF token for all AJAX requests
        window.Laravel = {
            csrfToken: '{{ csrf_token() }}'
        };

        // Setup axios defaults if using axios
        if (typeof axios !== 'undefined') {
            axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        }

        // Setup jQuery AJAX defaults if using jQuery
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
        }
    </script>

    @stack('scripts')
</body>
</html>
