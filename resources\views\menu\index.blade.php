@extends('layouts.app')

@section('title', 'เมนูอาหาร - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<!-- Hero Section -->
<section class="hero-section position-relative overflow-hidden" style="min-height: 30vh;">
    <div class="container position-relative">
        <div class="row align-items-center" style="min-height: 25vh;">
            <div class="col-lg-8 mx-auto text-center">
                <div class="fade-in-up">
                    <h1 class="h2 fw-bold mb-2 text-white text-shadow">
                        <i class="fas fa-utensils me-3 text-warning"></i>
                        เมนูอาหาร
                    </h1>
                    <p class="fs-6 mb-2 text-white-75">
                        ก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม พร้อมเครื่องเทศครบเครื่อง
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Filter -->
<section class="py-4 bg-white border-bottom">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex flex-wrap justify-content-center gap-2">
                    <a href="{{ route('menu.index') }}"
                       class="btn {{ !request('category') ? 'btn-primary' : 'btn-outline-primary' }} rounded-pill">
                        <i class="fas fa-th-large me-1"></i>ทั้งหมด
                    </a>
                    @foreach($categories as $category)
                        <a href="{{ route('menu.category', $category->slug) }}" 
                           class="btn {{ request('category') == $category->slug ? 'btn-primary' : 'btn-outline-primary' }} rounded-pill">
                            {{ $category->name }}
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Menu Section -->
@if($featuredMenus->count() > 0)
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold">
                    <i class="fas fa-star text-warning me-3"></i>เมนูแนะนำ
                </h2>
                <p class="lead text-muted">เมนูพิเศษที่ลูกค้าชื่นชอบมากที่สุด</p>
            </div>
        </div>
        
        <div class="row g-4">
            @foreach($featuredMenus as $index => $menu)
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-lg menu-card" style="animation-delay: {{ $index * 0.1 }}s;">
                        <!-- Featured Badge -->
                        <div class="position-absolute top-0 end-0 z-index-1">
                            <div class="bg-warning text-dark px-3 py-2 rounded-bottom-start">
                                <i class="fas fa-crown me-1"></i>แนะนำ
                            </div>
                        </div>
                        
                        @if($menu->image)
                            <div class="position-relative overflow-hidden">
                                <img src="{{ asset('storage/' . $menu->image) }}" 
                                     class="card-img-top menu-image" 
                                     alt="{{ $menu->name }}" 
                                     style="height: 250px; object-fit: cover;">
                            </div>
                        @else
                            <div class="card-img-top bg-gradient d-flex align-items-center justify-content-center" 
                                 style="height: 250px; background: linear-gradient(135deg, var(--light-brown), var(--cream-color));">
                                <i class="fas fa-utensils fa-4x text-primary opacity-50"></i>
                            </div>
                        @endif
                        
                        <div class="card-body d-flex flex-column">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h5 class="card-title text-primary fw-bold mb-0">{{ $menu->name }}</h5>
                                <span class="badge bg-primary rounded-pill">{{ $menu->category->name }}</span>
                            </div>
                            
                            <p class="card-text text-muted flex-grow-1 mb-3">{{ $menu->description }}</p>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="price-tag">
                                    <span class="h4 text-primary fw-bold mb-0">{{ $menu->formatted_price }}</span>
                                </div>
                                @auth
                                    <button class="btn btn-outline-primary btn-sm rounded-pill px-3">
                                        <i class="fas fa-heart me-1"></i>ชอบ
                                    </button>
                                @endauth
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- All Menu Section -->
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold">
                    <i class="fas fa-list me-3 text-primary"></i>
                    @if(request('category'))
                        {{ $currentCategory->name ?? 'เมนูอาหาร' }}
                    @else
                        เมนูอาหารทั้งหมด
                    @endif
                </h2>
            </div>
        </div>
        
        @if($menuItems->count() > 0)
            <div class="row g-4">
                @foreach($menuItems as $index => $menu)
                    <div class="col-lg-3 col-md-6">
                        <div class="card h-100 border-0 shadow menu-card-small" style="animation-delay: {{ $index * 0.05 }}s;">
                            @if($menu->image)
                                <div class="position-relative overflow-hidden">
                                    <img src="{{ asset('storage/' . $menu->image) }}" 
                                         class="card-img-top menu-image-small" 
                                         alt="{{ $menu->name }}" 
                                         style="height: 200px; object-fit: cover;">
                                    @if($menu->is_featured)
                                        <div class="position-absolute top-0 end-0 p-2">
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-star me-1"></i>แนะนำ
                                            </span>
                                        </div>
                                    @endif
                                </div>
                            @else
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                     style="height: 200px;">
                                    <i class="fas fa-utensils fa-3x text-muted opacity-50"></i>
                                </div>
                            @endif
                            
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title text-primary fw-bold mb-2">{{ $menu->name }}</h6>
                                <p class="card-text text-muted small flex-grow-1 mb-3">
                                    {{ Str::limit($menu->description, 80) }}
                                </p>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-primary fw-bold">{{ $menu->formatted_price }}</span>
                                    <small class="text-muted">{{ $menu->category->name }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($menuItems->hasPages())
                <div class="row mt-5">
                    <div class="col-12 d-flex justify-content-center">
                        {{ $menuItems->links() }}
                    </div>
                </div>
            @endif
        @else
            <div class="text-center py-5">
                <i class="fas fa-utensils fa-5x text-muted opacity-50 mb-4"></i>
                <h4 class="text-muted mb-3">ไม่พบเมนูอาหาร</h4>
                <p class="text-muted">
                    @if(request('category'))
                        ไม่มีเมนูในหมวดหมู่นี้ในขณะนี้
                    @else
                        ยังไม่มีเมนูอาหารในระบบ
                    @endif
                </p>
                <a href="{{ route('menu.index') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>ดูเมนูทั้งหมด
                </a>
            </div>
        @endif
    </div>
</section>

<style>
.menu-card {
    transition: all 0.4s ease;
    transform: translateY(0);
}

.menu-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.menu-card:hover .menu-image {
    transform: scale(1.05);
}

.menu-image {
    transition: transform 0.4s ease;
}

.menu-card-small {
    transition: all 0.3s ease;
}

.menu-card-small:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.menu-card-small:hover .menu-image-small {
    transform: scale(1.05);
}

.menu-image-small {
    transition: transform 0.3s ease;
}

.price-tag {
    position: relative;
}

.price-tag::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--gold-color));
    border-radius: 1px;
}

.z-index-1 {
    z-index: 1;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.text-white-75 {
    color: rgba(255,255,255,0.9) !important;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}
</style>
@endsection
