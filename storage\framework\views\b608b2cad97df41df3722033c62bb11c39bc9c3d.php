<?php $__env->startSection('title', 'หน้าติดต่อเรา - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 text-primary">
                    <i class="fas fa-phone me-2"></i>หน้าติดต่อเรา
                </h1>
                <a href="<?php echo e(route('admin.contact-page.edit')); ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>แก้ไขข้อมูล
                </a>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Content Cards -->
    <div class="row g-4">
        <!-- Basic Info -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>ข้อมูลหลัก
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>หัวข้อหน้า:</strong>
                        <p class="mb-0"><?php echo e($contactPage->title ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>คำอธิบาย:</strong>
                        <p class="mb-0"><?php echo e(Str::limit($contactPage->description ?: 'ยังไม่ได้กำหนด', 200)); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>รูป Hero:</strong>
                        <?php if($contactPage->hero_image): ?>
                            <div class="mt-2">
                                <img src="<?php echo e(asset('storage/' . $contactPage->hero_image)); ?>"
                                     alt="Hero Image" class="img-thumbnail" style="max-height: 100px;">
                            </div>
                        <?php else: ?>
                            <p class="mb-0 text-muted">ยังไม่ได้อัปโหลด</p>
                        <?php endif; ?>
                    </div>
                    <div class="mb-3">
                        <strong>รูปพื้นหลังเริ่มต้น:</strong>
                        <?php if($contactPage->default_background): ?>
                            <div class="mt-2">
                                <img src="<?php echo e(asset('storage/' . $contactPage->default_background)); ?>"
                                     alt="Default Background" class="img-thumbnail" style="max-height: 100px;">
                            </div>
                        <?php else: ?>
                            <p class="mb-0 text-muted">ยังไม่ได้อัปโหลด</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Details -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-address-book me-2"></i>ข้อมูลติดต่อ
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>ที่อยู่:</strong>
                        <p class="mb-0"><?php echo e($contactPage->address ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>โทรศัพท์:</strong>
                        <p class="mb-0"><?php echo e($contactPage->phone ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>มือถือ:</strong>
                        <p class="mb-0"><?php echo e($contactPage->mobile ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>อีเมล:</strong>
                        <p class="mb-0"><?php echo e($contactPage->email ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Media -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-share-alt me-2"></i>โซเชียลมีเดีย
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Line ID:</strong>
                        <p class="mb-0"><?php echo e($contactPage->line_id ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>Facebook:</strong>
                        <p class="mb-0"><?php echo e($contactPage->facebook ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>Instagram:</strong>
                        <p class="mb-0"><?php echo e($contactPage->instagram ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Opening Hours -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>เวลาทำการ
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>เวลาเปิด:</strong>
                        <p class="mb-0"><?php echo e($contactPage->open_time ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>เวลาปิด:</strong>
                        <p class="mb-0"><?php echo e($contactPage->close_time ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>วันที่เปิด:</strong>
                        <p class="mb-0"><?php echo e($contactPage->formatted_open_days ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>หมายเหตุ:</strong>
                        <p class="mb-0"><?php echo e($contactPage->special_hours ?: 'ไม่มี'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Location Images -->
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-images me-2"></i>รูปภาพสถานที่
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <strong>รูปหน้าร้าน:</strong>
                                <?php if($contactPage->location_image): ?>
                                    <div class="mt-2">
                                        <img src="<?php echo e(asset('storage/' . $contactPage->location_image)); ?>" 
                                             alt="หน้าร้าน" class="img-thumbnail" style="max-height: 100px;">
                                    </div>
                                <?php else: ?>
                                    <p class="mb-0 text-muted">ยังไม่ได้อัปโหลด</p>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <strong>รูปภายในร้าน:</strong>
                                <?php if($contactPage->interior_image): ?>
                                    <div class="mt-2">
                                        <img src="<?php echo e(asset('storage/' . $contactPage->interior_image)); ?>" 
                                             alt="ภายในร้าน" class="img-thumbnail" style="max-height: 100px;">
                                    </div>
                                <?php else: ?>
                                    <p class="mb-0 text-muted">ยังไม่ได้อัปโหลด</p>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <strong>รูปที่จอดรถ:</strong>
                                <?php if($contactPage->parking_image): ?>
                                    <div class="mt-2">
                                        <img src="<?php echo e(asset('storage/' . $contactPage->parking_image)); ?>" 
                                             alt="ที่จอดรถ" class="img-thumbnail" style="max-height: 100px;">
                                    </div>
                                <?php else: ?>
                                    <p class="mb-0 text-muted">ยังไม่ได้อัปโหลด</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Map & Location -->
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map me-2"></i>แผนที่และตำแหน่ง
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>ละติจูด:</strong>
                                <p class="mb-0"><?php echo e($contactPage->latitude ?: 'ยังไม่ได้กำหนด'); ?></p>
                            </div>
                            <div class="mb-3">
                                <strong>ลองจิจูด:</strong>
                                <p class="mb-0"><?php echo e($contactPage->longitude ?: 'ยังไม่ได้กำหนด'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Google Maps Embed:</strong>
                                <p class="mb-0"><?php echo e($contactPage->map_embed ? 'ได้กำหนดแล้ว' : 'ยังไม่ได้กำหนด'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <?php if($contactPage->directions): ?>
                    <div class="mb-3">
                        <strong>คำแนะนำการเดินทาง:</strong>
                        <p class="mb-0"><?php echo e(Str::limit($contactPage->directions, 200)); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Additional Info -->
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>ข้อมูลเพิ่มเติม
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <strong>ข้อมูลที่จอดรถ:</strong>
                                <p class="mb-0"><?php echo e(Str::limit($contactPage->parking_info ?: 'ยังไม่ได้กำหนด', 100)); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <strong>ขนส่งสาธารณะ:</strong>
                                <p class="mb-0"><?php echo e(Str::limit($contactPage->public_transport ?: 'ยังไม่ได้กำหนด', 100)); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <strong>ข้อมูลเพิ่มเติม:</strong>
                                <p class="mb-0"><?php echo e(Str::limit($contactPage->additional_info ?: 'ยังไม่ได้กำหนด', 100)); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-center gap-3">
                <a href="<?php echo e(route('contact')); ?>" target="_blank" class="btn btn-outline-primary">
                    <i class="fas fa-eye me-2"></i>ดูหน้าเว็บ
                </a>
                <a href="<?php echo e(route('admin.contact-page.edit')); ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>แก้ไขข้อมูล
                </a>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.card {
    transition: all 0.3s ease;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
    border-bottom: none !important;
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/admin/contact-page/index.blade.php ENDPATH**/ ?>