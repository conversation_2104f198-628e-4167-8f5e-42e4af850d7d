<?php $__env->startSection('title', 'หน้าหลัก - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startPush('styles'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.body.classList.add('hero-page');

    // Enhanced navbar scroll effect for hero page
    const navbar = document.querySelector('.navbar');

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section position-relative overflow-hidden">
    <div class="hero-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('<?php echo e($restaurantInfo->background_image ? asset('storage/' . $restaurantInfo->background_image) : asset('images/restaurant/background.jpg')); ?>'); background-size: cover; background-position: center;">
        <div class="container-fluid h-100">
            <div class="row h-100 align-items-center justify-content-center text-center">
                <div class="col-lg-8">
                    <div class="hero-content text-white">
                        <h1 class="hero-title display-3 fw-bold mb-4" data-aos="fade-up">
                            <?php echo e($restaurantInfo->name ?? 'ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

                        </h1>
                        <p class="hero-subtitle lead mb-4" data-aos="fade-up" data-aos-delay="200">
                            <?php echo e($restaurantInfo->description ?? 'ก๋วยเตี๋ยวเรือต้นตำรับ รสชาติดั้งเดิม ด้วยสูตรลับเฉพาะตัว เครื่องเทศครบครอง'); ?>

                        </p>
                        <p class="hero-tagline mb-5" data-aos="fade-up" data-aos-delay="400">
                            <?php echo e($restaurantInfo->tagline ?? 'ลิ้มลองรสชาติความอร่อยระดับตำนาน ที่ยังคงไว้ซึ่งเอกลักษณ์ไทย'); ?>

                        </p>
                        <div class="hero-buttons" data-aos="fade-up" data-aos-delay="600">
                            <a href="<?php echo e(route('menu.index')); ?>" class="btn btn-warning btn-lg me-3 px-5 py-3">
                                <i class="fas fa-utensils me-2"></i>ดูเมนูอาหาร
                            </a>
                            <?php if(auth()->guard()->check()): ?>
                                <?php if(Auth::user()->isAdmin()): ?>
                                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn btn-outline-light btn-lg px-5 py-3">
                                        <i class="fas fa-cog me-2"></i>จัดการระบบ
                                    </a>
                                <?php endif; ?>
                            <?php else: ?>
                                <a href="<?php echo e(route('contact')); ?>" class="btn btn-outline-light btn-lg px-5 py-3">
                                    <i class="fas fa-phone me-2"></i>ติดต่อเรา
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    </div>

    <!-- Scroll Indicator -->
    <div class="position-absolute bottom-0 start-50 translate-middle-x mb-4" style="z-index: 10;">
        <div class="text-white text-center">
            <small class="d-block mb-2">เลื่อนลงเพื่อดูเพิ่มเติม</small>
            <i class="fas fa-chevron-down fa-2x animate-bounce"></i>
        </div>
    </div>
</section>

<style>
/* Enhanced Hero Section Styles */
.hero-section {
    height: 100vh;
    min-height: 600px;
}

.hero-slide {
    height: 100vh;
    min-height: 600px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    position: relative;
}

/* Hero Overlay for Better Text Readability */
.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.4) 0%,
        rgba(139, 69, 19, 0.6) 50%,
        rgba(0, 0, 0, 0.4) 100%
    );
    z-index: 1;
}

/* Hero Content Card */
.hero-content-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 3rem 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}



/* Restaurant Title */
.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}

.restaurant-name {
    color: #ffffff;
    display: block;
}

.restaurant-highlight {
    color: #ffc107;
    display: block;
    font-size: 0.9em;
    margin-top: 0.5rem;
}

/* Hero Description */
.hero-description {
    margin: 2rem 0;
}

.lead-text {
    font-size: 1.4rem;
    font-weight: 500;
    line-height: 1.6;
    color: #ffffff;
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 1rem;
}

.sub-text {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    font-style: italic;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
}

/* Hero Action Buttons */
.hero-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: center;
}

.btn-hero-primary {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
    border: none;
    color: #000;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    box-shadow: 0 8px 20px rgba(255, 193, 7, 0.4);
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.btn-hero-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(255, 193, 7, 0.6);
    color: #000;
}

.btn-hero-secondary {
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    color: #ffffff;
}

/* Enhanced Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Enhanced Text Shadows */
.text-shadow {
    text-shadow:
        2px 2px 4px rgba(0, 0, 0, 0.5),
        0 0 10px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 0, 0, 0.2);
}

/* Fade In Animation */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

/* Button Hover Effects */
.btn-hero-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    transition: left 0.5s;
}

.btn-hero-primary:hover::before {
    left: 100%;
}

/* Glass Effect Enhancement */
.hero-content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.1) 100%
    );
    border-radius: 25px;
    pointer-events: none;
}







.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.animate-bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}


</style>

<!-- Featured Menu Section -->
<section class="py-5 bg-white position-relative" style="margin-top: 2rem;">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <div class="fade-in-up">
                    <h2 class="section-title display-5 fw-bold mb-4">
                        เมนูแนะนำ
                    </h2>
                    <p class="lead text-muted fs-5 mb-4">เมนูพิเศษที่ลูกค้าชื่นชอบมากที่สุด</p>
                    <div class="d-inline-block">
                        <div class="bg-warning" style="height: 3px; width: 80px; border-radius: 2px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <?php if($featuredMenus->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $featuredMenus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 border-0 shadow-lg position-relative overflow-hidden" style="animation-delay: <?php echo e($index * 0.1); ?>s;">
                            <!-- Featured Badge -->
                            <div class="position-absolute top-0 end-0 z-index-1">
                                <div class="bg-warning text-dark px-3 py-2 rounded-bottom-start">
                                    <i class="fas fa-crown me-1"></i>แนะนำ
                                </div>
                            </div>

                            <?php if($menu->image): ?>
                                <div class="position-relative overflow-hidden">
                                    <img src="<?php echo e(asset('storage/' . $menu->image)); ?>"
                                         class="card-img-top"
                                         alt="<?php echo e($menu->name); ?>"
                                         style="height: 250px; object-fit: cover; transition: transform 0.3s ease;">
                                    <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-25"></div>
                                </div>
                            <?php else: ?>
                                <div class="card-img-top bg-gradient d-flex align-items-center justify-content-center position-relative"
                                     style="height: 250px; background: linear-gradient(135deg, var(--light-brown), var(--cream-color));">
                                    <i class="fas fa-utensils fa-4x text-primary opacity-50"></i>
                                    <div class="position-absolute bottom-0 start-0 w-100 p-3 text-white">
                                        <small>รูปภาพจะเพิ่มเร็วๆ นี้</small>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="card-body d-flex flex-column p-4">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="card-title text-primary fw-bold mb-0"><?php echo e($menu->name); ?></h5>
                                    <span class="badge bg-primary rounded-pill"><?php echo e($menu->category->name); ?></span>
                                </div>

                                <p class="card-text text-muted flex-grow-1 mb-3"><?php echo e($menu->description); ?></p>

                                <div class="d-flex justify-content-between align-items-center mt-auto">
                                    <div class="price-tag">
                                        <span class="h4 text-primary fw-bold mb-0"><?php echo e($menu->formatted_price); ?></span>
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm rounded-pill px-3">
                                        <i class="fas fa-heart me-1"></i>ชอบ
                                    </button>
                                </div>
                            </div>

                            <!-- Hover Effect -->
                            <div class="position-absolute top-0 start-0 w-100 h-100 bg-primary bg-opacity-10 opacity-0 transition-opacity"></div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="text-center mt-5">
                <a href="<?php echo e(route('menu.index')); ?>" class="btn btn-primary btn-lg px-5 py-3 shadow-lg">
                    <i class="fas fa-utensils me-2"></i>ดูเมนูทั้งหมด
                    <i class="fas fa-arrow-right ms-2"></i>
                </a>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-utensils fa-5x text-muted opacity-50"></i>
                </div>
                <h4 class="text-muted mb-3">ยังไม่มีเมนูแนะนำ</h4>
                <p class="text-muted">เรากำลังเตรียมเมนูพิเศษสำหรับคุณ</p>
                <?php if(auth()->guard()->check()): ?>
                    <?php if(Auth::user()->isAdmin()): ?>
                        <a href="<?php echo e(route('admin.menu-items.create')); ?>" class="btn btn-primary mt-3">
                            <i class="fas fa-plus me-2"></i>เพิ่มเมนูแรก
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<style>
.card:hover .card-img-top {
    transform: scale(1.05);
}

.card:hover .transition-opacity {
    opacity: 1 !important;
}

.price-tag {
    position: relative;
}

.price-tag::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--gold-color));
    border-radius: 1px;
}

.z-index-1 {
    z-index: 1;
}
</style>

<!-- Categories Section -->
<section class="py-5 position-relative" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <div class="fade-in-up">
                    <h2 class="section-title display-4 fw-bold mb-4">
                        <i class="fas fa-th-large text-primary me-3"></i>หมวดหมู่อาหาร
                    </h2>
                    <p class="lead text-muted fs-5 mb-4">เลือกหมวดหมู่ที่คุณต้องการ พร้อมเมนูหลากหลายรสชาติ</p>
                    <div class="d-inline-block">
                        <div class="bg-primary" style="height: 3px; width: 80px; border-radius: 2px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <a href="<?php echo e(route('menu.category', $category->slug)); ?>" class="text-decoration-none">
                        <div class="card text-center h-100 border-0 shadow-lg category-card position-relative overflow-hidden"
                             style="animation-delay: <?php echo e($index * 0.1); ?>s;">

                            <!-- Category Icon Overlay -->
                            <div class="position-absolute top-0 end-0 p-3 z-index-1">
                                <div class="bg-white bg-opacity-90 rounded-circle p-2 shadow-sm">
                                    <?php if($category->icon): ?>
                                        <?php if(str_contains($category->icon, 'icon-boat-noodle')): ?>
                                            <span class="<?php echo e($category->icon); ?>"></span>
                                        <?php else: ?>
                                            <i class="<?php echo e($category->icon); ?> text-primary"></i>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="icon-boat-noodle"></span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if($category->image): ?>
                                <div class="position-relative overflow-hidden">
                                    <img src="<?php echo e(asset('storage/' . $category->image)); ?>"
                                         class="card-img-top category-image"
                                         alt="<?php echo e($category->name); ?>"
                                         style="height: 180px; object-fit: cover;">
                                    <div class="position-absolute bottom-0 start-0 w-100 h-50 bg-gradient-to-top"></div>
                                </div>
                            <?php else: ?>
                                <div class="card-img-top d-flex align-items-center justify-content-center position-relative"
                                     style="height: 180px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                                    <?php if($category->icon): ?>
                                        <?php if(str_contains($category->icon, 'icon-boat-noodle')): ?>
                                            <span class="<?php echo e($category->icon); ?> icon-boat-noodle-4x" style="filter: brightness(0) invert(1); opacity: 0.75;"></span>
                                        <?php else: ?>
                                            <i class="<?php echo e($category->icon); ?> fa-4x text-white opacity-75"></i>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="icon-boat-noodle icon-boat-noodle-4x" style="filter: brightness(0) invert(1); opacity: 0.75;"></span>
                                    <?php endif; ?>
                                    <div class="position-absolute bottom-0 start-0 w-100 p-3">
                                        <small class="text-white opacity-75"><?php echo e($category->name); ?></small>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="card-body d-flex flex-column p-4">
                                <h5 class="card-title text-primary fw-bold mb-3"><?php echo e($category->name); ?></h5>
                                <p class="card-text text-muted flex-grow-1 mb-3"><?php echo e($category->description); ?></p>

                                <div class="mt-auto">
                                    <div class="btn btn-outline-primary btn-sm rounded-pill px-4 category-btn">
                                        <i class="fas fa-eye me-2"></i>ดูเมนู
                                        <i class="fas fa-arrow-right ms-2 arrow-icon"></i>
                                    </div>

                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-utensils me-1"></i>
                                            <?php echo e($category->menuItems->count()); ?> รายการ
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Hover Overlay -->
                            <div class="position-absolute top-0 start-0 w-100 h-100 bg-primary bg-opacity-10 opacity-0 hover-overlay"></div>
                        </div>
                    </a>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>

<style>
.category-card {
    transition: all 0.4s ease;
    transform: translateY(0);
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.category-card:hover .category-image {
    transform: scale(1.1);
}

.category-card:hover .hover-overlay {
    opacity: 1 !important;
}

.category-card:hover .arrow-icon {
    transform: translateX(5px);
}

.category-image {
    transition: transform 0.4s ease;
}

.arrow-icon {
    transition: transform 0.3s ease;
}

.category-btn {
    transition: all 0.3s ease;
}

.category-card:hover .category-btn {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.bg-gradient-to-top {
    background: linear-gradient(to top, rgba(0,0,0,0.3), transparent);
}

.hover-overlay {
    transition: opacity 0.3s ease;
}

.z-index-1 {
    z-index: 1;
}
</style>


<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Enhanced Homepage Styles */
.hero-slide {
    position: relative;
    overflow: hidden;
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><path d="M0,10 Q25,0 50,10 T100,10 L100,20 L0,20 Z" fill="%23ffffff" opacity="0.1"/></svg>');
    background-size: 200px 20px;
    background-repeat: repeat-x;
    background-position: bottom;
    pointer-events: none;
}

.text-shadow {
    text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
}

.hover-shine {
    transition: opacity 0.3s ease;
}

.btn:hover .hover-shine {
    opacity: 0.1 !important;
}

/* Enhanced Card Animations */
.card {
    transform: translateY(20px);
    opacity: 0;
    animation: cardFadeIn 0.6s ease forwards;
}

@keyframes cardFadeIn {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

.category-card:hover {
    transform: translateY(-15px) scale(1.02);
}

/* Price Tag Enhancement */
.price-tag {
    position: relative;
}

.price-tag::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -10px;
    right: -10px;
    bottom: -5px;
    background: linear-gradient(135deg, var(--cream-color), var(--light-cream));
    border-radius: var(--border-radius-sm);
    z-index: -1;
    opacity: 0.7;
}

/* Section Dividers */
.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--gold-color), var(--primary-color));
    border-radius: 2px;
}



/* Enhanced Responsive Design */
@media (max-width: 992px) {
    .hero-title {
        font-size: 3rem;
    }

    .hero-content-card {
        padding: 2.5rem 1.5rem;
    }

    .lead-text {
        font-size: 1.3rem;
    }

    /* Featured Menu Section Responsive */
    .section-title.display-5 {
        font-size: 2.5rem !important;
    }
}

@media (max-width: 768px) {
    /* Featured Menu Section Mobile */
    .section-title.display-5 {
        font-size: 2rem !important;
        margin-bottom: 1rem !important;
    }

    /* Add more margin on mobile to avoid topbar overlap */
    section[style*="margin-top"] {
        margin-top: 3rem !important;
    }
}

@media (max-width: 576px) {
    /* Featured Menu Section Small Mobile */
    .section-title.display-5 {
        font-size: 1.75rem !important;
    }
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 70vh;
    }

    .hero-slide {
        min-height: 70vh;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-content-card {
        padding: 2rem 1.5rem;
        border-radius: 20px;
    }

    .lead-text {
        font-size: 1.2rem;
    }

    .sub-text {
        font-size: 1rem;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        gap: 0.8rem;
    }

    .display-4 {
        font-size: 2rem;
    }

    .card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        min-height: 60vh;
    }

    .hero-slide {
        min-height: 60vh;
    }

    .hero-title {
        font-size: 2rem;
    }

    .restaurant-name {
        font-size: 1em;
    }

    .restaurant-highlight {
        font-size: 0.8em;
    }

    .hero-content-card {
        padding: 1.5rem 1rem;
        border-radius: 15px;
    }

    .lead-text {
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .sub-text {
        font-size: 0.95rem;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        padding: 0.7rem 1.5rem;
        font-size: 0.95rem;
        width: 100%;
        max-width: 280px;
    }


}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Force page refresh if user just registered
<?php if(session('user_registered') || session('force_refresh')): ?>
    // Small delay to ensure session is properly set
    setTimeout(function() {
        // Force a hard refresh to ensure navigation updates
        window.location.href = window.location.href;
    }, 100);
<?php endif; ?>

// Also check if we need to update navigation after any auth change
document.addEventListener('DOMContentLoaded', function() {
    <?php if(session('user_registered')): ?>
        // Additional check to ensure navigation is updated
        setTimeout(function() {
            const navElement = document.querySelector('.navbar-nav');
            if (navElement && navElement.textContent.includes('Not logged in')) {
                location.reload();
            }
        }, 200);
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/home.blade.php ENDPATH**/ ?>