<?php $__env->startSection('title', 'หน้าหลัก - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Slider Section -->
<section class="hero-slider position-relative overflow-hidden">
    <div id="heroCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel" data-bs-interval="5000">
        <!-- Carousel Indicators -->
        <div class="carousel-indicators">
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="3"></button>
        </div>

        <!-- Carousel Items -->
        <div class="carousel-inner">
            <!-- Slide 1 -->
            <div class="carousel-item active">
                <div class="hero-slide" style="background: linear-gradient(rgba(139, 69, 19, 0.5), rgba(210, 105, 30, 0.5)), url('<?php echo e($restaurantInfo->background_image ? asset('storage/' . $restaurantInfo->background_image) : asset('images/restaurant/background.jpg')); ?>'); background-size: cover; background-position: center;">
                    <div class="container h-100 d-flex align-items-center">
                        <div class="row w-100">
                            <div class="col-lg-8 mx-auto text-center text-white">
                                <?php if(auth()->guard()->check()): ?>
                                    <div class="alert alert-success bg-white bg-opacity-95 text-dark mb-4 fade-in-up border-0 shadow-lg">
                                        <i class="fas fa-user-check me-2 text-success"></i>
                                        ยินดีต้อนรับ <strong class="text-primary"><?php echo e(Auth::user()->name); ?></strong>!
                                        <?php if(Auth::user()->isAdmin()): ?>
                                            <span class="badge bg-gradient bg-warning text-dark ms-2">
                                                <i class="fas fa-crown me-1"></i>ผู้ดูแลระบบ
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-gradient bg-info text-white ms-2">
                                                <i class="fas fa-user me-1"></i>สมาชิก
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <h1 class="display-3 fw-bold mb-4 text-shadow">
                                    <i class="fas fa-ship me-3 text-warning"></i>
                                    <span class="d-block d-md-inline">ร้านก๋วยเตี๋ยวเรือ</span>
                                    <span class="text-warning">เข้าท่า</span>
                                </h1>
                                <p class="lead mb-5 fs-4">
                                    <i class="fas fa-star text-warning me-2"></i>
                                    ก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม ด้วยน้ำซุปเข้มข้น เครื่องเทศครบเครื่อง<br>
                                    <small class="d-block mt-2 opacity-75">สืบทอดตำรับโบราณมาอย่างยาวนาน อร่อยถูกปากทุกคน</small>
                                </p>
                                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                                    <a href="<?php echo e(route('menu.index')); ?>" class="btn btn-primary btn-lg px-5 py-3 shadow-lg">
                                        <i class="fas fa-utensils me-2"></i>ดูเมนูอาหาร
                                    </a>
                                    <?php if(auth()->guard()->check()): ?>
                                        <?php if(Auth::user()->isAdmin()): ?>
                                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn btn-warning btn-lg px-5 py-3 shadow-lg">
                                                <i class="fas fa-cog me-2"></i>จัดการระบบ
                                            </a>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <a href="<?php echo e(route('register')); ?>" class="btn btn-success btn-lg px-5 py-3 shadow-lg">
                                            <i class="fas fa-user-plus me-2"></i>สมัครสมาชิก
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2 -->
            <div class="carousel-item">
                <div class="hero-slide" style="background: linear-gradient(rgba(139, 69, 19, 0.7), rgba(210, 105, 30, 0.7)), url('<?php echo e(asset('images/menu/noodle-beef-special.svg')); ?>');">
                    <div class="container h-100 d-flex align-items-center">
                        <div class="row w-100">
                            <div class="col-lg-8 mx-auto text-center text-white">
                                <h1 class="display-4 fw-bold mb-4 text-shadow">
                                    <i class="fas fa-star text-warning me-3"></i>เมนูแนะนำพิเศษ
                                </h1>
                                <p class="lead mb-5 fs-4">
                                    ลิ้มลองเมนูเด็ดที่ลูกค้าชื่นชอบมากที่สุด<br>
                                    <small class="d-block mt-2 opacity-75">ก๋วยเตี๋ยวเรือเนื้อพิเศษ และก๋วยเตี๋ยวเรือรวมมิตร</small>
                                </p>
                                <a href="<?php echo e(route('menu.category', 'recommended')); ?>" class="btn btn-warning btn-lg px-5 py-3 shadow-lg">
                                    <i class="fas fa-crown me-2"></i>ดูเมนูแนะนำ
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3 -->
            <div class="carousel-item">
                <div class="hero-slide" style="background: linear-gradient(rgba(139, 69, 19, 0.7), rgba(210, 105, 30, 0.7)), url('<?php echo e(asset('images/menu/mixed-noodle.svg')); ?>');">
                    <div class="container h-100 d-flex align-items-center">
                        <div class="row w-100">
                            <div class="col-lg-8 mx-auto text-center text-white">
                                <h1 class="display-4 fw-bold mb-4 text-shadow">
                                    <i class="fas fa-bowl-food text-warning me-3"></i>ก๋วยเตี๋ยวเรือแท้
                                </h1>
                                <p class="lead mb-5 fs-4">
                                    เนื้อนุ่ม หมูหวาน น้ำซุปเข้มข้น<br>
                                    <small class="d-block mt-2 opacity-75">เลือกได้ทั้งก๋วยเตี๋ยวเนื้อและหมู รสชาติดั้งเดิม</small>
                                </p>
                                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                                    <a href="<?php echo e(route('menu.category', 'noodle-beef')); ?>" class="btn btn-danger btn-lg px-4 py-3 shadow-lg">
                                        <i class="fas fa-drumstick-bite me-2"></i>ก๋วยเตี๋ยวเนื้อ
                                    </a>
                                    <a href="<?php echo e(route('menu.category', 'noodle-pork')); ?>" class="btn btn-warning btn-lg px-4 py-3 shadow-lg">
                                        <i class="fas fa-bacon me-2"></i>ก๋วยเตี๋ยวหมู
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4 -->
            <div class="carousel-item">
                <div class="hero-slide" style="background: linear-gradient(rgba(139, 69, 19, 0.7), rgba(210, 105, 30, 0.7)), url('<?php echo e(asset('images/menu/thai-tea.svg')); ?>');">
                    <div class="container h-100 d-flex align-items-center">
                        <div class="row w-100">
                            <div class="col-lg-8 mx-auto text-center text-white">
                                <h1 class="display-4 fw-bold mb-4 text-shadow">
                                    <i class="fas fa-coffee text-warning me-3"></i>ของทานเล่น & เครื่องดื่ม
                                </h1>
                                <p class="lead mb-5 fs-4">
                                    เต้าหู้ทอด หมูสะเต๊ะ และเครื่องดื่มสดชื่น<br>
                                    <small class="d-block mt-2 opacity-75">เหมาะสำหรับแชร์ หรือทานคู่กับก๋วยเตี๋ยว</small>
                                </p>
                                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                                    <a href="<?php echo e(route('menu.category', 'appetizers')); ?>" class="btn btn-info btn-lg px-4 py-3 shadow-lg">
                                        <i class="fas fa-cookie-bite me-2"></i>ของทานเล่น
                                    </a>
                                    <a href="<?php echo e(route('menu.category', 'beverages')); ?>" class="btn btn-success btn-lg px-4 py-3 shadow-lg">
                                        <i class="fas fa-coffee me-2"></i>เครื่องดื่ม
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Carousel Controls -->
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
            <span class="visually-hidden">Previous</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
            <span class="visually-hidden">Next</span>
        </button>
    </div>

    <!-- Scroll Indicator -->
    <div class="position-absolute bottom-0 start-50 translate-middle-x mb-4" style="z-index: 10;">
        <div class="text-white text-center">
            <small class="d-block mb-2">เลื่อนลงเพื่อดูเพิ่มเติม</small>
            <i class="fas fa-chevron-down fa-2x animate-bounce"></i>
        </div>
    </div>
</section>

<style>
.hero-slider {
    height: 100vh;
    min-height: 600px;
}

.hero-slide {
    height: 100vh;
    min-height: 600px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
}

.carousel-item {
    height: 100vh;
    min-height: 600px;
}

.carousel-indicators {
    bottom: 80px;
}

.carousel-indicators button {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    border: 2px solid white;
    background-color: transparent;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.carousel-indicators button.active {
    background-color: var(--gold-color);
    border-color: var(--gold-color);
    opacity: 1;
    transform: scale(1.2);
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 3rem;
    height: 3rem;
    background-size: 100%;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.3);
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.animate-bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.carousel-fade .carousel-item {
    opacity: 0;
    transition-duration: 1s;
    transition-property: opacity;
}

.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end {
    opacity: 1;
}

.carousel-fade .carousel-item-next,
.carousel-fade .carousel-item-prev {
    transform: none;
}
</style>

<!-- Featured Menu Section -->
<section class="py-5 bg-white position-relative">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold">
                    <i class="fas fa-star text-warning me-3"></i>เมนูแนะนำ
                </h2>
                <p class="lead text-muted">เมนูพิเศษที่ลูกค้าชื่นชอบมากที่สุด</p>
            </div>
        </div>

        <?php if($featuredMenus->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $featuredMenus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 border-0 shadow-lg position-relative overflow-hidden" style="animation-delay: <?php echo e($index * 0.1); ?>s;">
                            <!-- Featured Badge -->
                            <div class="position-absolute top-0 end-0 z-index-1">
                                <div class="bg-warning text-dark px-3 py-2 rounded-bottom-start">
                                    <i class="fas fa-crown me-1"></i>แนะนำ
                                </div>
                            </div>

                            <?php if($menu->image): ?>
                                <div class="position-relative overflow-hidden">
                                    <img src="<?php echo e(asset('storage/' . $menu->image)); ?>"
                                         class="card-img-top"
                                         alt="<?php echo e($menu->name); ?>"
                                         style="height: 250px; object-fit: cover; transition: transform 0.3s ease;">
                                    <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-25"></div>
                                </div>
                            <?php else: ?>
                                <div class="card-img-top bg-gradient d-flex align-items-center justify-content-center position-relative"
                                     style="height: 250px; background: linear-gradient(135deg, var(--light-brown), var(--cream-color));">
                                    <i class="fas fa-utensils fa-4x text-primary opacity-50"></i>
                                    <div class="position-absolute bottom-0 start-0 w-100 p-3 text-white">
                                        <small>รูปภาพจะเพิ่มเร็วๆ นี้</small>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="card-body d-flex flex-column p-4">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="card-title text-primary fw-bold mb-0"><?php echo e($menu->name); ?></h5>
                                    <span class="badge bg-primary rounded-pill"><?php echo e($menu->category->name); ?></span>
                                </div>

                                <p class="card-text text-muted flex-grow-1 mb-3"><?php echo e($menu->description); ?></p>

                                <div class="d-flex justify-content-between align-items-center mt-auto">
                                    <div class="price-tag">
                                        <span class="h4 text-primary fw-bold mb-0"><?php echo e($menu->formatted_price); ?></span>
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm rounded-pill px-3">
                                        <i class="fas fa-heart me-1"></i>ชอบ
                                    </button>
                                </div>
                            </div>

                            <!-- Hover Effect -->
                            <div class="position-absolute top-0 start-0 w-100 h-100 bg-primary bg-opacity-10 opacity-0 transition-opacity"></div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="text-center mt-5">
                <a href="<?php echo e(route('menu.index')); ?>" class="btn btn-primary btn-lg px-5 py-3 shadow-lg">
                    <i class="fas fa-utensils me-2"></i>ดูเมนูทั้งหมด
                    <i class="fas fa-arrow-right ms-2"></i>
                </a>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-utensils fa-5x text-muted opacity-50"></i>
                </div>
                <h4 class="text-muted mb-3">ยังไม่มีเมนูแนะนำ</h4>
                <p class="text-muted">เรากำลังเตรียมเมนูพิเศษสำหรับคุณ</p>
                <?php if(auth()->guard()->check()): ?>
                    <?php if(Auth::user()->isAdmin()): ?>
                        <a href="<?php echo e(route('admin.menu-items.create')); ?>" class="btn btn-primary mt-3">
                            <i class="fas fa-plus me-2"></i>เพิ่มเมนูแรก
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<style>
.card:hover .card-img-top {
    transform: scale(1.05);
}

.card:hover .transition-opacity {
    opacity: 1 !important;
}

.price-tag {
    position: relative;
}

.price-tag::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--gold-color));
    border-radius: 1px;
}

.z-index-1 {
    z-index: 1;
}
</style>

<!-- Categories Section -->
<section class="py-5 position-relative" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="section-title display-5 fw-bold">
                    <i class="fas fa-th-large text-primary me-3"></i>หมวดหมู่อาหาร
                </h2>
                <p class="lead text-muted">เลือกหมวดหมู่ที่คุณต้องการ</p>
            </div>
        </div>

        <div class="row g-4">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <a href="<?php echo e(route('menu.category', $category->slug)); ?>" class="text-decoration-none">
                        <div class="card text-center h-100 border-0 shadow-lg category-card position-relative overflow-hidden"
                             style="animation-delay: <?php echo e($index * 0.1); ?>s;">

                            <!-- Category Icon Overlay -->
                            <div class="position-absolute top-0 end-0 p-3 z-index-1">
                                <div class="bg-white bg-opacity-90 rounded-circle p-2 shadow-sm">
                                    <i class="fas fa-utensils text-primary"></i>
                                </div>
                            </div>

                            <?php if($category->image): ?>
                                <div class="position-relative overflow-hidden">
                                    <img src="<?php echo e(asset('storage/' . $category->image)); ?>"
                                         class="card-img-top category-image"
                                         alt="<?php echo e($category->name); ?>"
                                         style="height: 180px; object-fit: cover;">
                                    <div class="position-absolute bottom-0 start-0 w-100 h-50 bg-gradient-to-top"></div>
                                </div>
                            <?php else: ?>
                                <div class="card-img-top d-flex align-items-center justify-content-center position-relative"
                                     style="height: 180px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                                    <i class="fas fa-utensils fa-4x text-white opacity-75"></i>
                                    <div class="position-absolute bottom-0 start-0 w-100 p-3">
                                        <small class="text-white opacity-75"><?php echo e($category->name); ?></small>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="card-body d-flex flex-column p-4">
                                <h5 class="card-title text-primary fw-bold mb-3"><?php echo e($category->name); ?></h5>
                                <p class="card-text text-muted flex-grow-1 mb-3"><?php echo e($category->description); ?></p>

                                <div class="mt-auto">
                                    <div class="btn btn-outline-primary btn-sm rounded-pill px-4 category-btn">
                                        <i class="fas fa-eye me-2"></i>ดูเมนู
                                        <i class="fas fa-arrow-right ms-2 arrow-icon"></i>
                                    </div>

                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-utensils me-1"></i>
                                            <?php echo e($category->menuItems->count()); ?> รายการ
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Hover Overlay -->
                            <div class="position-absolute top-0 start-0 w-100 h-100 bg-primary bg-opacity-10 opacity-0 hover-overlay"></div>
                        </div>
                    </a>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>

<style>
.category-card {
    transition: all 0.4s ease;
    transform: translateY(0);
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.category-card:hover .category-image {
    transform: scale(1.1);
}

.category-card:hover .hover-overlay {
    opacity: 1 !important;
}

.category-card:hover .arrow-icon {
    transform: translateX(5px);
}

.category-image {
    transition: transform 0.4s ease;
}

.arrow-icon {
    transition: transform 0.3s ease;
}

.category-btn {
    transition: all 0.3s ease;
}

.category-card:hover .category-btn {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.bg-gradient-to-top {
    background: linear-gradient(to top, rgba(0,0,0,0.3), transparent);
}

.hover-overlay {
    transition: opacity 0.3s ease;
}

.z-index-1 {
    z-index: 1;
}
</style>

<!-- About Section -->
<section class="py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="section-title text-start">เกี่ยวกับเรา</h2>
                <p class="lead">
                    ร้านก๋วยเตี๋ยวเรือเข้าท่า ก่อตั้งขึ้นด้วยความรักในอาหารไทยดั้งเดิม 
                    โดยเฉพาะก๋วยเตี๋ยวเรือที่มีรสชาติเข้มข้น หอมหวน
                </p>
                <p>
                    เราใช้วัตถุดิบคุณภาพดี เครื่องเทศแท้ และสูตรลับที่สืบทอดมาจากรุ่นสู่รุ่น 
                    เพื่อมอบประสบการณ์การรับประทานอาหารที่ดีที่สุดให้กับลูกค้าทุกท่าน
                </p>
                <a href="<?php echo e(route('about')); ?>" class="btn btn-primary">
                    <i class="fas fa-info-circle me-2"></i>อ่านเพิ่มเติม
                </a>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <img src="<?php echo e(asset('images/restaurant/boat-noodle-shop.jpg')); ?>"
                         alt="ร้านก๋วยเตี๋ยวเรือเข้าท่า" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-5 bg-white">
    <div class="container">
        <h2 class="section-title">ติดต่อเรา</h2>
        
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-map-marker-alt fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">ที่อยู่</h5>
                        <p class="card-text">
                            123 ถนนริมน้า<br>
                            เขตพระนคร กรุงเทพฯ 10200
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-phone fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">โทรศัพท์</h5>
                        <p class="card-text">
                            02-123-4567<br>
                            ************
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-clock fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">เวลาเปิด-ปิด</h5>
                        <p class="card-text">
                            เปิดทุกวัน<br>
                            08:00 - 20:00 น.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Force page refresh if user just registered
<?php if(session('user_registered') || session('force_refresh')): ?>
    // Small delay to ensure session is properly set
    setTimeout(function() {
        // Force a hard refresh to ensure navigation updates
        window.location.href = window.location.href;
    }, 100);
<?php endif; ?>

// Also check if we need to update navigation after any auth change
document.addEventListener('DOMContentLoaded', function() {
    <?php if(session('user_registered')): ?>
        // Additional check to ensure navigation is updated
        setTimeout(function() {
            const navElement = document.querySelector('.navbar-nav');
            if (navElement && navElement.textContent.includes('Not logged in')) {
                location.reload();
            }
        }, 200);
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/home.blade.php ENDPATH**/ ?>