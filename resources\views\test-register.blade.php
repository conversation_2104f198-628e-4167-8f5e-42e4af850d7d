<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการสมัครสมาชิก</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4>ทดสอบการสมัครสมาชิก</h4>
                    </div>
                    <div class="card-body">
                        @if(session('success'))
                            <div class="alert alert-success">{{ session('success') }}</div>
                        @endif
                        
                        @if(session('error'))
                            <div class="alert alert-danger">{{ session('error') }}</div>
                        @endif
                        
                        @if($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        
                        @auth
                            <div class="alert alert-success">
                                <h6>เข้าสู่ระบบแล้ว!</h6>
                                <p class="mb-1"><strong>ชื่อ:</strong> {{ Auth::user()->name }}</p>
                                <p class="mb-1"><strong>อีเมล:</strong> {{ Auth::user()->email }}</p>
                                <p class="mb-1"><strong>บทบาท:</strong> {{ Auth::user()->role }}</p>
                                
                                <div class="mt-3">
                                    <a href="{{ route('home') }}" class="btn btn-info me-2">ไปยังหน้าหลัก</a>
                                    
                                    <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-danger">ออกจากระบบ</button>
                                    </form>
                                </div>
                            </div>
                        @else
                            <form method="POST" action="{{ route('test.register.post') }}">
                                @csrf
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">ชื่อ-นามสกุล</label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ old('name', 'ทดสอบ ผู้ใช้') }}" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">อีเมล</label>
                                    <input type="email" class="form-control" id="email" name="email" value="{{ old('email', '<EMAIL>') }}" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">รหัสผ่าน</label>
                                    <input type="password" class="form-control" id="password" name="password" value="12345678" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">ยืนยันรหัสผ่าน</label>
                                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" value="12345678" required>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">สมัครสมาชิก</button>
                            </form>
                            
                            <hr>
                            
                            <div class="text-center">
                                <h6>ข้อมูลทดสอบ:</h6>
                                <p class="mb-1"><strong>ชื่อ:</strong> ทดสอบ ผู้ใช้</p>
                                <p class="mb-1"><strong>อีเมล:</strong> <EMAIL></p>
                                <p class="mb-1"><strong>รหัสผ่าน:</strong> 12345678</p>
                            </div>
                        @endauth
                        
                        <div class="mt-3">
                            <h6>ลิงก์ทดสอบ:</h6>
                            <a href="{{ route('register') }}" class="btn btn-outline-primary btn-sm me-2">หน้า Register ปกติ</a>
                            <a href="{{ route('login') }}" class="btn btn-outline-success btn-sm me-2">หน้า Login</a>
                            <a href="{{ route('test.login') }}" class="btn btn-outline-info btn-sm me-2">ทดสอบ Login</a>
                            <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-sm">หน้าหลัก</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
