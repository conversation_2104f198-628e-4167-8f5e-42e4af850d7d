<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RestaurantInfo extends Model
{
    use HasFactory;

    protected $table = 'restaurant_info';

    protected $fillable = [
        'name',
        'description',
        'address',
        'phone',
        'mobile',
        'email',
        'website',
        'facebook',
        'line',
        'instagram',
        'open_time',
        'close_time',
        'open_days',
        'logo',
        'cover_image',
        'map_embed',
        'latitude',
        'longitude',
        'is_active'
    ];

    protected $casts = [
        'open_days' => 'array',
        'is_active' => 'boolean',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    // Get the restaurant info (singleton pattern)
    public static function getInfo()
    {
        return static::where('is_active', true)->first() ?? new static();
    }

    // Get formatted open days
    public function getFormattedOpenDaysAttribute()
    {
        if (!$this->open_days) {
            return 'ทุกวัน';
        }

        $days = [
            'monday' => 'จันทร์',
            'tuesday' => 'อังคาร',
            'wednesday' => 'พุธ',
            'thursday' => 'พฤหัสบดี',
            'friday' => 'ศุกร์',
            'saturday' => 'เสาร์',
            'sunday' => 'อาทิตย์'
        ];

        $openDays = array_map(function($day) use ($days) {
            return $days[$day] ?? $day;
        }, $this->open_days);

        return implode(', ', $openDays);
    }

    // Get formatted opening hours
    public function getFormattedOpeningHoursAttribute()
    {
        if (!$this->open_time || !$this->close_time) {
            return 'ตามสอบถาม';
        }

        return $this->open_time . ' - ' . $this->close_time . ' น.';
    }
}
