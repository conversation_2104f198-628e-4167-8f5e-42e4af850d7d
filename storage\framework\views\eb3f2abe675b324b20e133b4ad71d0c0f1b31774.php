<?php $__env->startSection('title', 'หน้าเกี่ยวกับเรา - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 text-primary">
                    <i class="fas fa-info-circle me-2"></i>หน้าเกี่ยวกับเรา
                </h1>
                <a href="<?php echo e(route('admin.about-page.edit')); ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>แก้ไขข้อมูล
                </a>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Content Cards -->
    <div class="row g-4">
        <!-- Basic Info -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>เนื้อหาหลัก
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>หัวข้อหน้า:</strong>
                        <p class="mb-0"><?php echo e($aboutPage->title ?: 'ยังไม่ได้กำหนด'); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>เนื้อหาหลัก:</strong>
                        <p class="mb-0"><?php echo e(Str::limit($aboutPage->content ?: 'ยังไม่ได้กำหนด', 200)); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Story Content -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>เรื่องราวและวิสัยทัศน์
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>เรื่องราวของเรา:</strong>
                        <p class="mb-0"><?php echo e(Str::limit($aboutPage->our_story ?: 'ยังไม่ได้กำหนด', 100)); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>เนื้อหาหลัก:</strong>
                        <p class="mb-0"><?php echo e(Str::limit($aboutPage->main_content ?: 'ยังไม่ได้กำหนด', 100)); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>พันธกิจ:</strong>
                        <p class="mb-0"><?php echo e(Str::limit($aboutPage->our_mission ?: 'ยังไม่ได้กำหนด', 100)); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>วิสัยทัศน์:</strong>
                        <p class="mb-0"><?php echo e(Str::limit($aboutPage->our_vision ?: 'ยังไม่ได้กำหนด', 100)); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Images -->
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-images me-2"></i>รูปภาพหลัก
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>
                                <i class="fas fa-image me-2 text-primary"></i>รูป Hero
                                <span class="badge bg-primary ms-2">หน้าหลัก</span>
                            </h6>
                            <?php if($aboutPage->hero_image): ?>
                                <div class="text-center p-3 bg-light rounded">
                                    <img src="<?php echo e(asset('storage/' . $aboutPage->hero_image)); ?>" 
                                         alt="รูป Hero" 
                                         class="img-thumbnail mb-2" 
                                         style="max-height: 150px;">
                                    <div class="small text-success">
                                        <i class="fas fa-check-circle me-1"></i>กำลังใช้งาน
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="bg-light p-4 text-center text-muted rounded">
                                    <i class="fas fa-image fa-3x mb-2"></i>
                                    <p class="mb-1">ยังไม่มีรูป Hero</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4">
                            <h6>
                                <i class="fas fa-image me-2 text-info"></i>รูปเรื่องราว
                            </h6>
                            <?php if($aboutPage->story_image): ?>
                                <div class="text-center p-3 bg-light rounded">
                                    <img src="<?php echo e(asset('storage/' . $aboutPage->story_image)); ?>" 
                                         alt="รูปเรื่องราว" 
                                         class="img-thumbnail mb-2" 
                                         style="max-height: 150px;">
                                    <div class="small text-success">
                                        <i class="fas fa-check-circle me-1"></i>กำลังใช้งาน
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="bg-light p-4 text-center text-muted rounded">
                                    <i class="fas fa-image fa-3x mb-2"></i>
                                    <p class="mb-1">ยังไม่มีรูปเรื่องราว</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4">
                            <h6>
                                <i class="fas fa-image me-2 text-success"></i>รูปทีมงาน
                            </h6>
                            <?php if($aboutPage->team_image): ?>
                                <div class="text-center p-3 bg-light rounded">
                                    <img src="<?php echo e(asset('storage/' . $aboutPage->team_image)); ?>" 
                                         alt="รูปทีมงาน" 
                                         class="img-thumbnail mb-2" 
                                         style="max-height: 150px;">
                                    <div class="small text-success">
                                        <i class="fas fa-check-circle me-1"></i>กำลังใช้งาน
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="bg-light p-4 text-center text-muted rounded">
                                    <i class="fas fa-image fa-3x mb-2"></i>
                                    <p class="mb-1">ยังไม่มีรูปทีมงาน</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gallery Images -->
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-photo-video me-2"></i>แกลเลอรี่
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>รูปแกลเลอรี่ 1</h6>
                            <?php if($aboutPage->gallery_image_1): ?>
                                <img src="<?php echo e(asset('storage/' . $aboutPage->gallery_image_1)); ?>" 
                                     alt="แกลเลอรี่ 1" 
                                     class="img-thumbnail" 
                                     style="max-height: 150px;">
                            <?php else: ?>
                                <div class="bg-light p-4 text-center text-muted rounded">
                                    <i class="fas fa-image fa-2x mb-2"></i>
                                    <p class="mb-0">ยังไม่มีรูป</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4">
                            <h6>รูปแกลเลอรี่ 2</h6>
                            <?php if($aboutPage->gallery_image_2): ?>
                                <img src="<?php echo e(asset('storage/' . $aboutPage->gallery_image_2)); ?>" 
                                     alt="แกลเลอรี่ 2" 
                                     class="img-thumbnail" 
                                     style="max-height: 150px;">
                            <?php else: ?>
                                <div class="bg-light p-4 text-center text-muted rounded">
                                    <i class="fas fa-image fa-2x mb-2"></i>
                                    <p class="mb-0">ยังไม่มีรูป</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4">
                            <h6>รูปแกลเลอรี่ 3</h6>
                            <?php if($aboutPage->gallery_image_3): ?>
                                <img src="<?php echo e(asset('storage/' . $aboutPage->gallery_image_3)); ?>" 
                                     alt="แกลเลอรี่ 3" 
                                     class="img-thumbnail" 
                                     style="max-height: 150px;">
                            <?php else: ?>
                                <div class="bg-light p-4 text-center text-muted rounded">
                                    <i class="fas fa-image fa-2x mb-2"></i>
                                    <p class="mb-0">ยังไม่มีรูป</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
    border-bottom: none !important;
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/admin/about-page/index.blade.php ENDPATH**/ ?>