@extends('layouts.app')

@section('title', 'สมัครสมาชิก - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="min-vh-100 d-flex align-items-center" style="background: linear-gradient(135deg, var(--cream-color) 0%, #FFF8DC 100%);">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <!-- Logo Section -->
                <div class="text-center mb-4">
                    <a href="{{ route('home') }}" class="text-decoration-none">
                        <div class="bg-white rounded-circle shadow-lg mx-auto mb-3 d-flex align-items-center justify-content-center"
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-ship fa-2x text-primary"></i>
                        </div>
                        <h2 class="text-primary fw-bold">ร้านก๋วยเตี๋ยวเรือเข้าท่า</h2>
                    </a>
                    <p class="text-muted">สมัครสมาชิกเพื่อรับสิทธิพิเศษ</p>
                </div>

                <div class="card border-0 shadow-lg">
                    <div class="card-header text-center border-0" style="background: linear-gradient(135deg, var(--secondary-color), var(--gold-color));">
                        <h4 class="mb-0 text-white">
                            <i class="fas fa-user-plus me-2"></i>สมัครสมาชิก
                        </h4>
                    </div>
                
                <div class="card-body p-5">
                    <form method="POST" action="{{ route('register') }}" class="needs-validation" novalidate id="registerForm">
                        @csrf
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">ชื่อ-นามสกุล</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" 
                                       class="form-control @error('name') is-invalid @enderror" 
                                       id="name" 
                                       name="name" 
                                       value="{{ old('name') }}" 
                                       required 
                                       autocomplete="name" 
                                       autofocus
                                       placeholder="กรุณากรอกชื่อ-นามสกุล">
                            </div>
                            @error('name')
                                <div class="invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">อีเมล</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" 
                                       class="form-control @error('email') is-invalid @enderror" 
                                       id="email" 
                                       name="email" 
                                       value="{{ old('email') }}" 
                                       required 
                                       autocomplete="email"
                                       placeholder="กรุณากรอกอีเมล">
                            </div>
                            @error('email')
                                <div class="invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">รหัสผ่าน</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       id="password" 
                                       name="password" 
                                       required 
                                       autocomplete="new-password"
                                       placeholder="กรุณากรอกรหัสผ่าน (อย่างน้อย 8 ตัวอักษร)">
                            </div>
                            @error('password')
                                <div class="invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="password_confirmation" class="form-label">ยืนยันรหัสผ่าน</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" 
                                       class="form-control" 
                                       id="password_confirmation" 
                                       name="password_confirmation" 
                                       required 
                                       autocomplete="new-password"
                                       placeholder="กรุณายืนยันรหัสผ่าน">
                            </div>
                        </div>
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-success btn-lg py-3 shadow-sm">
                                <i class="fas fa-user-plus me-2"></i>สมัครสมาชิก
                            </button>
                        </div>

                        <div class="text-center">
                            <small class="text-muted">
                                มีบัญชีอยู่แล้ว?
                                <a href="{{ route('login') }}" class="text-primary text-decoration-none fw-bold">เข้าสู่ระบบ</a>
                            </small>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <a href="{{ route('home') }}" class="text-secondary text-decoration-none">
                                        <i class="fas fa-arrow-left me-1"></i>กลับหน้าหลัก
                                    </a>
                                </small>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
                <!-- Benefits -->
                <div class="card mt-4 border-0" style="background: linear-gradient(135deg, #e8f5e8, #f0f8f0);">
                    <div class="card-body text-center">
                        <h6 class="card-title text-success mb-4">
                            <i class="fas fa-gift me-2"></i>ประโยชน์ของการเป็นสมาชิก
                        </h6>
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-percent text-success me-2"></i>
                                    <small>โปรโมชั่นพิเศษ</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-star text-warning me-2"></i>
                                    <small>สะสมแต้ม</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-bell text-info me-2"></i>
                                    <small>ข่าวสารใหม่</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    <small>สั่งล่วงหน้า</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Form validation and debug
document.getElementById('registerForm').addEventListener('submit', function(e) {
    console.log('Form submitted');
    console.log('Action:', this.action);
    console.log('Method:', this.method);
});
</script>
@endpush
