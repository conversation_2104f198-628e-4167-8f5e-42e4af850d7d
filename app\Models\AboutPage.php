<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AboutPage extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'hero_image',
        'story_image',
        'team_image',
        'gallery_image_1',
        'gallery_image_2',
        'gallery_image_3',
        'our_story',
        'our_mission',
        'our_vision',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Get the about page info (singleton pattern)
    public static function getInfo()
    {
        return static::where('is_active', true)->first() ?? new static();
    }
}
