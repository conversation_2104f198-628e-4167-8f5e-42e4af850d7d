@extends('layouts.app')

@section('title', 'แก้ไขผู้ใช้ - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary fade-in-up">
                        <i class="fas fa-user-edit me-2"></i>แก้ไขผู้ใช้
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.users.index') }}">ผู้ใช้</a>
                            </li>
                            <li class="breadcrumb-item active">แก้ไข</li>
                        </ol>
                    </nav>
                </div>
                <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>กลับ
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-edit me-2"></i>ข้อมูลผู้ใช้
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('admin.users.update', $user) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">ชื่อ-นามสกุล <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">อีเมล <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">บทบาท <span class="text-danger">*</span></label>
                                    <select class="form-select @error('role') is-invalid @enderror" id="role" name="role" required>
                                        <option value="">เลือกบทบาท</option>
                                        <option value="user" {{ old('role', $user->role) == 'user' ? 'selected' : '' }}>ผู้ใช้ทั่วไป</option>
                                        <option value="admin" {{ old('role', $user->role) == 'admin' ? 'selected' : '' }}>ผู้ดูแลระบบ</option>
                                    </select>
                                    @error('role')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">รหัสผ่านใหม่</label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                           id="password" name="password">
                                    <small class="text-muted">เว้นว่างไว้หากไม่ต้องการเปลี่ยนรหัสผ่าน</small>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password_confirmation" class="form-label">ยืนยันรหัสผ่านใหม่</label>
                            <input type="password" class="form-control" id="password_confirmation" name="password_confirmation">
                            <small class="text-muted">กรอกรหัสผ่านใหม่อีกครั้งเพื่อยืนยัน</small>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>ยกเลิก
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Info Card -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-info-circle me-2"></i>ข้อมูลผู้ใช้
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-user fa-2x text-white"></i>
                        </div>
                        <h6 class="mt-2 mb-1">{{ $user->name }}</h6>
                        <small class="text-muted">{{ $user->email }}</small>
                    </div>

                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h6 text-primary mb-1">
                                    @if($user->role == 'admin')
                                        <i class="fas fa-crown text-warning"></i> Admin
                                    @else
                                        <i class="fas fa-user text-info"></i> User
                                    @endif
                                </div>
                                <small class="text-muted">บทบาท</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h6 text-success mb-1">{{ $user->created_at->format('d/m/Y') }}</div>
                            <small class="text-muted">สมัครเมื่อ</small>
                        </div>
                    </div>

                    <hr>

                    <div class="small text-muted">
                        <div class="d-flex justify-content-between mb-2">
                            <span>สร้างเมื่อ:</span>
                            <span>{{ $user->created_at->format('d/m/Y H:i') }}</span>
                        </div>
                        @if($user->updated_at != $user->created_at)
                            <div class="d-flex justify-content-between mb-2">
                                <span>แก้ไขล่าสุด:</span>
                                <span>{{ $user->updated_at->format('d/m/Y H:i') }}</span>
                            </div>
                        @endif
                        @if($user->email_verified_at)
                            <div class="d-flex justify-content-between">
                                <span>ยืนยันอีเมล:</span>
                                <span class="text-success">
                                    <i class="fas fa-check-circle"></i> แล้ว
                                </span>
                            </div>
                        @else
                            <div class="d-flex justify-content-between">
                                <span>ยืนยันอีเมล:</span>
                                <span class="text-warning">
                                    <i class="fas fa-clock"></i> รอดำเนินการ
                                </span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Warning Card -->
            @if($user->role == 'admin')
                <div class="card border-warning mt-4">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>คำเตือน
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="small mb-0">
                            ผู้ใช้นี้เป็นผู้ดูแลระบบ การเปลี่ยนแปลงข้อมูลอาจส่งผลต่อการเข้าถึงระบบ
                            กรุณาตรวจสอบข้อมูลให้ถูกต้องก่อนบันทึก
                        </p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('password_confirmation');
    
    // Show/hide password confirmation field based on password input
    passwordInput.addEventListener('input', function() {
        if (this.value.length > 0) {
            confirmPasswordInput.parentElement.style.display = 'block';
            confirmPasswordInput.required = true;
        } else {
            confirmPasswordInput.parentElement.style.display = 'none';
            confirmPasswordInput.required = false;
            confirmPasswordInput.value = '';
        }
    });
    
    // Initial state
    if (passwordInput.value.length === 0) {
        confirmPasswordInput.parentElement.style.display = 'none';
        confirmPasswordInput.required = false;
    }
});
</script>
@endpush
@endsection
