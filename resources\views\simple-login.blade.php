<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; }
        input { padding: 8px; width: 300px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .error { color: red; margin: 10px 0; }
        .success { color: green; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Simple Login Test</h1>
    
    @if(session('success'))
        <div class="success">{{ session('success') }}</div>
    @endif
    
    @if($errors->any())
        <div class="error">
            @foreach($errors->all() as $error)
                <div>{{ $error }}</div>
            @endforeach
        </div>
    @endif
    
    <form method="POST" action="{{ route('simple.login.post') }}">
        @csrf
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" value="{{ old('email', '<EMAIL>') }}" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" value="admin123" required>
        </div>
        
        <div class="form-group">
            <button type="submit">Login</button>
        </div>
    </form>
    
    <hr>
    
    <h3>Debug Info:</h3>
    <p><strong>Auth Check:</strong> {{ Auth::check() ? 'Logged In' : 'Not Logged In' }}</p>
    @if(Auth::check())
        <p><strong>User:</strong> {{ Auth::user()->name }} ({{ Auth::user()->email }})</p>
        <p><strong>Role:</strong> {{ Auth::user()->role }}</p>
        <p><strong>Is Admin:</strong> {{ Auth::user()->isAdmin() ? 'Yes' : 'No' }}</p>
    @endif
    
    <p><strong>Session ID:</strong> {{ session()->getId() }}</p>
    
    <hr>
    
    <h3>Quick Links:</h3>
    <ul>
        <li><a href="{{ route('home') }}">Home</a></li>
        <li><a href="{{ route('login') }}">Regular Login</a></li>
        @if(Auth::check() && Auth::user()->isAdmin())
            <li><a href="{{ route('admin.dashboard') }}">Admin Dashboard</a></li>
        @endif
        @if(Auth::check())
            <li>
                <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                    @csrf
                    <button type="submit">Logout</button>
                </form>
            </li>
        @endif
    </ul>
</body>
</html>
