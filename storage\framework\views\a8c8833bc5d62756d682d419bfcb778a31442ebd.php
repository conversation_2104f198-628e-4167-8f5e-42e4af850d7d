<?php $__env->startSection('title', 'เข้าสู่ระบบ - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-vh-100 d-flex align-items-center" style="background: linear-gradient(135deg, var(--cream-color) 0%, #FFF8DC 100%);">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <!-- Logo Section -->
                <div class="text-center mb-4">
                    <a href="<?php echo e(route('home')); ?>" class="text-decoration-none">
                        <div class="bg-white rounded-circle shadow-lg mx-auto mb-3 d-flex align-items-center justify-content-center"
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-bowl-food fa-2x text-primary"></i>
                        </div>
                        <h2 class="text-primary fw-bold">ร้านก๋วยเตี๋ยวเรือเข้าท่า</h2>
                    </a>
                    <p class="text-muted">เข้าสู่ระบบเพื่อใช้งาน</p>
                </div>

                <div class="card border-0 shadow-lg">
                    <div class="card-header text-center border-0" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                        <h4 class="mb-0 text-white">
                            <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
                        </h4>
                    </div>
                
                <div class="card-body p-5">
                    <form method="POST" action="<?php echo e(route('login')); ?>" class="needs-validation" novalidate>
                        <?php echo csrf_field(); ?>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">อีเมล</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" 
                                       class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="email" 
                                       name="email" 
                                       value="<?php echo e(old('email')); ?>" 
                                       required 
                                       autocomplete="email" 
                                       autofocus
                                       placeholder="กรุณากรอกอีเมล">
                            </div>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback d-block">
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">รหัสผ่าน</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" 
                                       class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="password" 
                                       name="password" 
                                       required 
                                       autocomplete="current-password"
                                       placeholder="กรุณากรอกรหัสผ่าน">
                            </div>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback d-block">
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                จดจำการเข้าสู่ระบบ
                            </label>
                        </div>
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg py-3 shadow-sm">
                                <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
                            </button>
                        </div>

                        <div class="text-center">
                            <small class="text-muted">
                                ยังไม่มีบัญชี?
                                <a href="<?php echo e(route('register')); ?>" class="text-primary text-decoration-none fw-bold">สมัครสมาชิก</a>
                            </small>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <a href="<?php echo e(route('home')); ?>" class="text-secondary text-decoration-none">
                                        <i class="fas fa-arrow-left me-1"></i>กลับหน้าหลัก
                                    </a>
                                </small>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
                <!-- Demo Credentials -->
                <div class="card mt-4 border-0 bg-light">
                    <div class="card-body text-center">
                        <h6 class="card-title text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>ข้อมูลสำหรับทดสอบ
                        </h6>
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <small class="text-muted d-block">อีเมล</small>
                                <code class="text-primary"><EMAIL></code>
                            </div>
                            <div class="col-6">
                                <small class="text-muted d-block">รหัสผ่าน</small>
                                <code class="text-primary">admin123</code>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary rounded-pill px-4" onclick="fillDemoCredentials()">
                            <i class="fas fa-magic me-1"></i>กรอกข้อมูลทดสอบ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function fillDemoCredentials() {
    document.getElementById('email').value = '<EMAIL>';
    document.getElementById('password').value = 'admin123';
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodle\resources\views/auth/login.blade.php ENDPATH**/ ?>