@extends('layouts.app')

@section('title', 'หน้าหลัก - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<!-- Hero Slider Section -->
<section class="hero-slider position-relative overflow-hidden">
    <div id="heroCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel" data-bs-interval="5000">
        <!-- Carousel Indicators -->
        <div class="carousel-indicators">
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="3"></button>
        </div>

        <!-- Carousel Items -->
        <div class="carousel-inner">
            <!-- Slide 1 -->
            <div class="carousel-item active">
                <div class="hero-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('{{ $restaurantInfo->background_image ? asset('storage/' . $restaurantInfo->background_image) : asset('images/restaurant/background.jpg') }}'); background-size: cover; background-position: center;">
                    <!-- Background Overlay for Better Text Readability -->
                    <div class="hero-overlay"></div>

                    <div class="container h-100 d-flex align-items-center position-relative">
                        <div class="row w-100">
                            <div class="col-lg-10 mx-auto text-center text-white">
                                <!-- Main Content Card -->
                                <div class="hero-content-card">
                                    <div class="fade-in-up">
                                        <!-- Restaurant Name -->
                                        <h1 class="hero-title mb-4">
                                            <span class="restaurant-name">{{ $restaurantInfo->name ?? 'ร้านก๋วยเตี๋ยวเรือ' }}</span>
                                            <span class="restaurant-highlight">เข้าท่า</span>
                                        </h1>

                                        <!-- Description -->
                                        <div class="hero-description mb-5">
                                            <p class="lead-text">
                                                {{ $restaurantInfo->description ?? 'ก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม ด้วยน้ำซุปเข้มข้น เครื่องเทศครบเครื่อง' }}
                                            </p>
                                            <p class="sub-text">
                                                สืบทอดตำรับโบราณมาอย่างยาวนาน อร่อยถูกปากทุกคน
                                            </p>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="hero-actions fade-in-up" style="animation-delay: 0.6s;">
                                            <a href="{{ route('menu.index') }}" class="btn btn-hero-primary">
                                                <i class="fas fa-utensils me-2"></i>ดูเมนูอาหาร
                                            </a>
                                            @auth
                                                @if(Auth::user()->isAdmin())
                                                    <a href="{{ route('admin.dashboard') }}" class="btn btn-hero-secondary">
                                                        <i class="fas fa-cog me-2"></i>จัดการระบบ
                                                    </a>
                                                @endif
                                            @else
                                                <a href="{{ route('contact') }}" class="btn btn-hero-secondary">
                                                    <i class="fas fa-phone me-2"></i>ติดต่อเรา
                                                </a>
                                            @endauth
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2 -->
            <div class="carousel-item">
                <div class="hero-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('{{ asset('images/menu/noodle-beef-special.svg') }}'); background-size: cover; background-position: center;">
                    <div class="hero-overlay"></div>
                    <div class="container h-100 d-flex align-items-center position-relative">
                        <div class="row w-100">
                            <div class="col-lg-10 mx-auto text-center text-white">
                                <div class="hero-content-card">
                                    <h1 class="hero-title mb-4">
                                        <span class="restaurant-name">เมนูแนะนำพิเศษ</span>
                                    </h1>
                                    <div class="hero-description mb-5">
                                        <p class="lead-text">
                                            ลิ้มลองเมนูเด็ดที่ลูกค้าชื่นชอบมากที่สุด
                                        </p>
                                        <p class="sub-text">
                                            ก๋วยเตี๋ยวเรือเนื้อพิเศษ และก๋วยเตี๋ยวเรือรวมมิตร
                                        </p>
                                    </div>
                                    <div class="hero-actions">
                                        <a href="{{ route('menu.category', 'recommended') }}" class="btn btn-hero-primary">
                                            <i class="fas fa-crown me-2"></i>ดูเมนูแนะนำ
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3 -->
            <div class="carousel-item">
                <div class="hero-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('{{ asset('images/menu/mixed-noodle.svg') }}'); background-size: cover; background-position: center;">
                    <div class="hero-overlay"></div>
                    <div class="container h-100 d-flex align-items-center position-relative">
                        <div class="row w-100">
                            <div class="col-lg-10 mx-auto text-center text-white">
                                <div class="hero-content-card">
                                    <h1 class="hero-title mb-4">
                                        <span class="restaurant-name">ก๋วยเตี๋ยวเรือแท้</span>
                                    </h1>
                                    <div class="hero-description mb-5">
                                        <p class="lead-text">
                                            เนื้อนุ่ม หมูหวาน น้ำซุปเข้มข้น
                                        </p>
                                        <p class="sub-text">
                                            เลือกได้ทั้งก๋วยเตี๋ยวเนื้อและหมู รสชาติดั้งเดิม
                                        </p>
                                    </div>
                                    <div class="hero-actions">
                                        <a href="{{ route('menu.category', 'noodle-beef') }}" class="btn btn-hero-primary" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); box-shadow: 0 8px 20px rgba(220, 53, 69, 0.4);">
                                            <i class="fas fa-drumstick-bite me-2"></i>ก๋วยเตี๋ยวเนื้อ
                                        </a>
                                        <a href="{{ route('menu.category', 'noodle-pork') }}" class="btn btn-hero-primary">
                                            <i class="fas fa-bacon me-2"></i>ก๋วยเตี๋ยวหมู
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4 -->
            <div class="carousel-item">
                <div class="hero-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('{{ asset('images/menu/thai-tea.svg') }}'); background-size: cover; background-position: center;">
                    <div class="hero-overlay"></div>
                    <div class="container h-100 d-flex align-items-center position-relative">
                        <div class="row w-100">
                            <div class="col-lg-10 mx-auto text-center text-white">
                                <div class="hero-content-card">
                                    <h1 class="hero-title mb-4">
                                        <span class="restaurant-name">ของทานเล่น & เครื่องดื่ม</span>
                                    </h1>
                                    <div class="hero-description mb-5">
                                        <p class="lead-text">
                                            เต้าหู้ทอด หมูสะเต๊ะ และเครื่องดื่มสดชื่น
                                        </p>
                                        <p class="sub-text">
                                            เหมาะสำหรับแชร์ หรือทานคู่กับก๋วยเตี๋ยว
                                        </p>
                                    </div>
                                    <div class="hero-actions">
                                        <a href="{{ route('menu.category', 'appetizers') }}" class="btn btn-hero-primary" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); box-shadow: 0 8px 20px rgba(23, 162, 184, 0.4);">
                                            <i class="fas fa-cookie-bite me-2"></i>ของทานเล่น
                                        </a>
                                        <a href="{{ route('menu.category', 'beverages') }}" class="btn btn-hero-primary" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);">
                                            <i class="fas fa-coffee me-2"></i>เครื่องดื่ม
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Carousel Controls -->
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
            <span class="visually-hidden">Previous</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
            <span class="visually-hidden">Next</span>
        </button>
    </div>

    <!-- Scroll Indicator -->
    <div class="position-absolute bottom-0 start-50 translate-middle-x mb-4" style="z-index: 10;">
        <div class="text-white text-center">
            <small class="d-block mb-2">เลื่อนลงเพื่อดูเพิ่มเติม</small>
            <i class="fas fa-chevron-down fa-2x animate-bounce"></i>
        </div>
    </div>
</section>

<style>
/* Enhanced Hero Section Styles */
.hero-slider {
    height: 100vh;
    min-height: 600px;
}

.hero-slide {
    height: 100vh;
    min-height: 600px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    position: relative;
}

/* Hero Overlay for Better Text Readability */
.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.4) 0%,
        rgba(139, 69, 19, 0.6) 50%,
        rgba(0, 0, 0, 0.4) 100%
    );
    z-index: 1;
}

/* Hero Content Card */
.hero-content-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 3rem 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}



/* Restaurant Title */
.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}

.restaurant-name {
    color: #ffffff;
    display: block;
}

.restaurant-highlight {
    color: #ffc107;
    display: block;
    font-size: 0.9em;
    margin-top: 0.5rem;
}

/* Hero Description */
.hero-description {
    margin: 2rem 0;
}

.lead-text {
    font-size: 1.4rem;
    font-weight: 500;
    line-height: 1.6;
    color: #ffffff;
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 1rem;
}

.sub-text {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    font-style: italic;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
}

/* Hero Action Buttons */
.hero-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: center;
}

.btn-hero-primary {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
    border: none;
    color: #000;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    box-shadow: 0 8px 20px rgba(255, 193, 7, 0.4);
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.btn-hero-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(255, 193, 7, 0.6);
    color: #000;
}

.btn-hero-secondary {
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    color: #ffffff;
}

/* Enhanced Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Enhanced Text Shadows */
.text-shadow {
    text-shadow:
        2px 2px 4px rgba(0, 0, 0, 0.5),
        0 0 10px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 0, 0, 0.2);
}

/* Fade In Animation */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

/* Button Hover Effects */
.btn-hero-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    transition: left 0.5s;
}

.btn-hero-primary:hover::before {
    left: 100%;
}

/* Glass Effect Enhancement */
.hero-content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.1) 100%
    );
    border-radius: 25px;
    pointer-events: none;
}

.carousel-item {
    height: 100vh;
    min-height: 600px;
}

.carousel-indicators {
    bottom: 80px;
}

.carousel-indicators button {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    border: 2px solid white;
    background-color: transparent;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.carousel-indicators button.active {
    background-color: var(--gold-color);
    border-color: var(--gold-color);
    opacity: 1;
    transform: scale(1.2);
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 3rem;
    height: 3rem;
    background-size: 100%;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.3);
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.animate-bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.carousel-fade .carousel-item {
    opacity: 0;
    transition-duration: 1s;
    transition-property: opacity;
}

.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end {
    opacity: 1;
}

.carousel-fade .carousel-item-next,
.carousel-fade .carousel-item-prev {
    transform: none;
}
</style>

<!-- Featured Menu Section -->
<section class="py-5 bg-white position-relative">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <div class="fade-in-up">
                    <h2 class="section-title display-4 fw-bold mb-4">
                        <i class="fas fa-star text-warning me-3 pulse-animation"></i>เมนูแนะนำ
                    </h2>
                    <p class="lead text-muted fs-5 mb-4">เมนูพิเศษที่ลูกค้าชื่นชอบมากที่สุด</p>
                    <div class="d-inline-block">
                        <div class="bg-warning" style="height: 3px; width: 80px; border-radius: 2px;"></div>
                    </div>
                </div>
            </div>
        </div>

        @if($featuredMenus->count() > 0)
            <div class="row g-4">
                @foreach($featuredMenus as $index => $menu)
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 border-0 shadow-lg position-relative overflow-hidden" style="animation-delay: {{ $index * 0.1 }}s;">
                            <!-- Featured Badge -->
                            <div class="position-absolute top-0 end-0 z-index-1">
                                <div class="bg-warning text-dark px-3 py-2 rounded-bottom-start">
                                    <i class="fas fa-crown me-1"></i>แนะนำ
                                </div>
                            </div>

                            @if($menu->image)
                                <div class="position-relative overflow-hidden">
                                    <img src="{{ asset('storage/' . $menu->image) }}"
                                         class="card-img-top"
                                         alt="{{ $menu->name }}"
                                         style="height: 250px; object-fit: cover; transition: transform 0.3s ease;">
                                    <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-25"></div>
                                </div>
                            @else
                                <div class="card-img-top bg-gradient d-flex align-items-center justify-content-center position-relative"
                                     style="height: 250px; background: linear-gradient(135deg, var(--light-brown), var(--cream-color));">
                                    <i class="fas fa-utensils fa-4x text-primary opacity-50"></i>
                                    <div class="position-absolute bottom-0 start-0 w-100 p-3 text-white">
                                        <small>รูปภาพจะเพิ่มเร็วๆ นี้</small>
                                    </div>
                                </div>
                            @endif

                            <div class="card-body d-flex flex-column p-4">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="card-title text-primary fw-bold mb-0">{{ $menu->name }}</h5>
                                    <span class="badge bg-primary rounded-pill">{{ $menu->category->name }}</span>
                                </div>

                                <p class="card-text text-muted flex-grow-1 mb-3">{{ $menu->description }}</p>

                                <div class="d-flex justify-content-between align-items-center mt-auto">
                                    <div class="price-tag">
                                        <span class="h4 text-primary fw-bold mb-0">{{ $menu->formatted_price }}</span>
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm rounded-pill px-3">
                                        <i class="fas fa-heart me-1"></i>ชอบ
                                    </button>
                                </div>
                            </div>

                            <!-- Hover Effect -->
                            <div class="position-absolute top-0 start-0 w-100 h-100 bg-primary bg-opacity-10 opacity-0 transition-opacity"></div>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="text-center mt-5">
                <a href="{{ route('menu.index') }}" class="btn btn-primary btn-lg px-5 py-3 shadow-lg">
                    <i class="fas fa-utensils me-2"></i>ดูเมนูทั้งหมด
                    <i class="fas fa-arrow-right ms-2"></i>
                </a>
            </div>
        @else
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-utensils fa-5x text-muted opacity-50"></i>
                </div>
                <h4 class="text-muted mb-3">ยังไม่มีเมนูแนะนำ</h4>
                <p class="text-muted">เรากำลังเตรียมเมนูพิเศษสำหรับคุณ</p>
                @auth
                    @if(Auth::user()->isAdmin())
                        <a href="{{ route('admin.menu-items.create') }}" class="btn btn-primary mt-3">
                            <i class="fas fa-plus me-2"></i>เพิ่มเมนูแรก
                        </a>
                    @endif
                @endauth
            </div>
        @endif
    </div>
</section>

<style>
.card:hover .card-img-top {
    transform: scale(1.05);
}

.card:hover .transition-opacity {
    opacity: 1 !important;
}

.price-tag {
    position: relative;
}

.price-tag::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--gold-color));
    border-radius: 1px;
}

.z-index-1 {
    z-index: 1;
}
</style>

<!-- Categories Section -->
<section class="py-5 position-relative" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <div class="fade-in-up">
                    <h2 class="section-title display-4 fw-bold mb-4">
                        <i class="fas fa-th-large text-primary me-3"></i>หมวดหมู่อาหาร
                    </h2>
                    <p class="lead text-muted fs-5 mb-4">เลือกหมวดหมู่ที่คุณต้องการ พร้อมเมนูหลากหลายรสชาติ</p>
                    <div class="d-inline-block">
                        <div class="bg-primary" style="height: 3px; width: 80px; border-radius: 2px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            @foreach($categories as $index => $category)
                <div class="col-lg-3 col-md-6 mb-4">
                    <a href="{{ route('menu.category', $category->slug) }}" class="text-decoration-none">
                        <div class="card text-center h-100 border-0 shadow-lg category-card position-relative overflow-hidden"
                             style="animation-delay: {{ $index * 0.1 }}s;">

                            <!-- Category Icon Overlay -->
                            <div class="position-absolute top-0 end-0 p-3 z-index-1">
                                <div class="bg-white bg-opacity-90 rounded-circle p-2 shadow-sm">
                                    @if($category->icon)
                                        @if(str_contains($category->icon, 'icon-boat-noodle'))
                                            <span class="{{ $category->icon }}"></span>
                                        @else
                                            <i class="{{ $category->icon }} text-primary"></i>
                                        @endif
                                    @else
                                        <span class="icon-boat-noodle"></span>
                                    @endif
                                </div>
                            </div>

                            @if($category->image)
                                <div class="position-relative overflow-hidden">
                                    <img src="{{ asset('storage/' . $category->image) }}"
                                         class="card-img-top category-image"
                                         alt="{{ $category->name }}"
                                         style="height: 180px; object-fit: cover;">
                                    <div class="position-absolute bottom-0 start-0 w-100 h-50 bg-gradient-to-top"></div>
                                </div>
                            @else
                                <div class="card-img-top d-flex align-items-center justify-content-center position-relative"
                                     style="height: 180px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                                    @if($category->icon)
                                        @if(str_contains($category->icon, 'icon-boat-noodle'))
                                            <span class="{{ $category->icon }} icon-boat-noodle-4x" style="filter: brightness(0) invert(1); opacity: 0.75;"></span>
                                        @else
                                            <i class="{{ $category->icon }} fa-4x text-white opacity-75"></i>
                                        @endif
                                    @else
                                        <span class="icon-boat-noodle icon-boat-noodle-4x" style="filter: brightness(0) invert(1); opacity: 0.75;"></span>
                                    @endif
                                    <div class="position-absolute bottom-0 start-0 w-100 p-3">
                                        <small class="text-white opacity-75">{{ $category->name }}</small>
                                    </div>
                                </div>
                            @endif

                            <div class="card-body d-flex flex-column p-4">
                                <h5 class="card-title text-primary fw-bold mb-3">{{ $category->name }}</h5>
                                <p class="card-text text-muted flex-grow-1 mb-3">{{ $category->description }}</p>

                                <div class="mt-auto">
                                    <div class="btn btn-outline-primary btn-sm rounded-pill px-4 category-btn">
                                        <i class="fas fa-eye me-2"></i>ดูเมนู
                                        <i class="fas fa-arrow-right ms-2 arrow-icon"></i>
                                    </div>

                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-utensils me-1"></i>
                                            {{ $category->menuItems->count() }} รายการ
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Hover Overlay -->
                            <div class="position-absolute top-0 start-0 w-100 h-100 bg-primary bg-opacity-10 opacity-0 hover-overlay"></div>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>

<style>
.category-card {
    transition: all 0.4s ease;
    transform: translateY(0);
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.category-card:hover .category-image {
    transform: scale(1.1);
}

.category-card:hover .hover-overlay {
    opacity: 1 !important;
}

.category-card:hover .arrow-icon {
    transform: translateX(5px);
}

.category-image {
    transition: transform 0.4s ease;
}

.arrow-icon {
    transition: transform 0.3s ease;
}

.category-btn {
    transition: all 0.3s ease;
}

.category-card:hover .category-btn {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.bg-gradient-to-top {
    background: linear-gradient(to top, rgba(0,0,0,0.3), transparent);
}

.hover-overlay {
    transition: opacity 0.3s ease;
}

.z-index-1 {
    z-index: 1;
}
</style>


@endsection

@push('styles')
<style>
/* Enhanced Homepage Styles */
.hero-slide {
    position: relative;
    overflow: hidden;
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><path d="M0,10 Q25,0 50,10 T100,10 L100,20 L0,20 Z" fill="%23ffffff" opacity="0.1"/></svg>');
    background-size: 200px 20px;
    background-repeat: repeat-x;
    background-position: bottom;
    pointer-events: none;
}

.text-shadow {
    text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
}

.hover-shine {
    transition: opacity 0.3s ease;
}

.btn:hover .hover-shine {
    opacity: 0.1 !important;
}

/* Enhanced Card Animations */
.card {
    transform: translateY(20px);
    opacity: 0;
    animation: cardFadeIn 0.6s ease forwards;
}

@keyframes cardFadeIn {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

.category-card:hover {
    transform: translateY(-15px) scale(1.02);
}

/* Price Tag Enhancement */
.price-tag {
    position: relative;
}

.price-tag::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -10px;
    right: -10px;
    bottom: -5px;
    background: linear-gradient(135deg, var(--cream-color), var(--light-cream));
    border-radius: var(--border-radius-sm);
    z-index: -1;
    opacity: 0.7;
}

/* Section Dividers */
.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--gold-color), var(--primary-color));
    border-radius: 2px;
}

/* Enhanced Carousel Indicators */
.carousel-indicators [data-bs-target] {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    background: transparent;
    transition: all 0.3s ease;
}

.carousel-indicators [data-bs-target].active {
    background: var(--gold-color);
    transform: scale(1.2);
}

/* Enhanced Responsive Design */
@media (max-width: 992px) {
    .hero-title {
        font-size: 3rem;
    }

    .hero-content-card {
        padding: 2.5rem 1.5rem;
    }

    .lead-text {
        font-size: 1.3rem;
    }
}

@media (max-width: 768px) {
    .hero-slider {
        min-height: 70vh;
    }

    .hero-slide {
        min-height: 70vh;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-content-card {
        padding: 2rem 1.5rem;
        border-radius: 20px;
    }

    .lead-text {
        font-size: 1.2rem;
    }

    .sub-text {
        font-size: 1rem;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        gap: 0.8rem;
    }

    .display-4 {
        font-size: 2rem;
    }

    .card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-slider {
        min-height: 60vh;
    }

    .hero-slide {
        min-height: 60vh;
    }

    .hero-title {
        font-size: 2rem;
    }

    .restaurant-name {
        font-size: 1em;
    }

    .restaurant-highlight {
        font-size: 0.8em;
    }

    .hero-content-card {
        padding: 1.5rem 1rem;
        border-radius: 15px;
    }

    .lead-text {
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .sub-text {
        font-size: 0.95rem;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        padding: 0.7rem 1.5rem;
        font-size: 0.95rem;
        width: 100%;
        max-width: 280px;
    }


}
</style>
@endpush

@push('scripts')
<script>
// Force page refresh if user just registered
@if(session('user_registered') || session('force_refresh'))
    // Small delay to ensure session is properly set
    setTimeout(function() {
        // Force a hard refresh to ensure navigation updates
        window.location.href = window.location.href;
    }, 100);
@endif

// Also check if we need to update navigation after any auth change
document.addEventListener('DOMContentLoaded', function() {
    @if(session('user_registered'))
        // Additional check to ensure navigation is updated
        setTimeout(function() {
            const navElement = document.querySelector('.navbar-nav');
            if (navElement && navElement.textContent.includes('Not logged in')) {
                location.reload();
            }
        }, 200);
    @endif
});
</script>
@endpush
