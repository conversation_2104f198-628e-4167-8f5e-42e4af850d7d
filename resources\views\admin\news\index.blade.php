@extends('layouts.app')

@section('title', 'จัดการข่าวสาร - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary btn-back">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับสู่หน้าหลัก
            </a>
        </div>
    </div>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary fade-in-up">
                        <i class="fas fa-newspaper me-2"></i>จัดการข่าวสาร
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item active">ข่าวสาร</li>
                        </ol>
                    </nav>
                </div>
                <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มข่าวสารใหม่
                </a>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- News List -->
    <div class="card border-0 shadow-lg">
        <div class="card-header">
            <h5 class="mb-0 text-white">
                <i class="fas fa-list me-2"></i>รายการข่าวสาร
            </h5>
        </div>
        <div class="card-body p-0">
            @if($news->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="60">#</th>
                                <th>หัวข้อ</th>
                                <th width="120">สถานะ</th>
                                <th width="120">แนะนำ</th>
                                <th width="150">วันที่เผยแพร่</th>
                                <th width="120">ผู้สร้าง</th>
                                <th width="150">จัดการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($news as $item)
                                <tr>
                                    <td>{{ $item->sort_order ?: $item->id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($item->image)
                                                <img src="{{ asset('storage/' . $item->image) }}" 
                                                     alt="{{ $item->title }}" 
                                                     class="rounded me-3" 
                                                     style="width: 50px; height: 50px; object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <h6 class="mb-1">{{ $item->title }}</h6>
                                                <small class="text-muted">{{ $item->short_content }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if($item->is_published)
                                            <span class="badge bg-success">
                                                <i class="fas fa-eye me-1"></i>เผยแพร่
                                            </span>
                                        @else
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-eye-slash me-1"></i>ร่าง
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($item->is_featured)
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-star me-1"></i>แนะนำ
                                            </span>
                                        @else
                                            <span class="badge bg-light text-dark">
                                                <i class="far fa-star me-1"></i>ทั่วไป
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ $item->formatted_published_at }}
                                        </small>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ $item->creator->name }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ route('admin.news.show', $item) }}" 
                                               class="btn btn-outline-info" 
                                               title="ดู">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.news.edit', $item) }}" 
                                               class="btn btn-outline-warning" 
                                               title="แก้ไข">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.news.destroy', $item) }}" 
                                                  method="POST" 
                                                  class="d-inline"
                                                  onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบข่าวสารนี้?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" 
                                                        class="btn btn-outline-danger" 
                                                        title="ลบ">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($news->hasPages())
                    <div class="card-footer bg-light">
                        {{ $news->links() }}
                    </div>
                @endif
            @else
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">ยังไม่มีข่าวสาร</h5>
                    <p class="text-muted">เริ่มต้นสร้างข่าวสารแรกของคุณ</p>
                    <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>เพิ่มข่าวสารใหม่
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
